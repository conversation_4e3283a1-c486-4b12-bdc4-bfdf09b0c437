import React from 'react';
import {observer, inject} from 'mobx-react';
import {IMainStore} from '@/store';
import {Layout, toast, Input, Modal, Button, confirm} from 'amis';
import {RouteComponentProps, Switch} from 'react-router';

import AMISRenderer from '@/component/AMISRenderer';
import {configureManagerEditorPlugin} from '@/editor/DisabledEditorPlugin'; // 用于隐藏一些不需要的Editor预置组件
import {showRenderers} from '@/editor/disabledRenderers';
import {MoveClassOrPage} from '@/views/applyPage/component/moveClassOrPage/index';
import dajian from '@/image/common_icons/dajian.png';
import gongzuo from '@/image/common_icons/gongzuo.png';
import {getSystemComponentList} from '@/utils/api/api';

// 页面样式
import './index.scss';
import './nav-item-settings.scss';
// 选择创建页面类型组件
import InitPageType from '@/component/InitPageType/index';
// 页面表单页面组件
import PageContent from '@/component/PageContent/index';
// 页面流程页面组件
import PageProcessContent from '@/component/PageProcessContent/index';
// 页面报表页面组件
import PageReportContent from '@/component/PageReportContent/index';
// 页面链接页面组件
import PagelinkContent from '@/component/PagelinkContent/index';
// wiki页面组件
import PageWikiContent from '@/component/PageWikiContent/index';
// 页面自定义页面组件
import PageCustompage from '@/component/PageCustompage/index';
// 页面增删改查页面组件
import PageCRUDpage from '@/component/PageCRUDpage/index';
// 页面数据源导入页面组件
import PageDataSetImport from '@/component/PageDataSetImport/index';
// 页面amis报表页面大屏页面组件
import PageDashboardContent from '@/component/PageDashboardContent/index';
// 页面云网盘
import PageYunWangPanContent from '@/component/PageYunWangPanContent/index';
// 页面笔记
import PageNotesContent from '@/component/PageNotesContent/index';
// 页面多维度表格页面组件
import PageMultiDimensionalTableContent from '@/component/PageMultiDimensionalTableContent/index';
// 页面白板页面组件
import PageWhiteboardContent from '@/component/PageWhiteboardContent/index';
// 页面芋道网盘
import PageYudaoPanContent from '@/component/PageYudaoPanContent/index';
// 页面数据管理
import PageDataManage from '@/component/PageDataManage/index';
// 门户
import PagePortletContent from '@/component/PagePortletpage/index';
// 应用主页审批组件
import {default as Approval} from '@/views/approval/index';

// 图片引入
import arrow_down_icon from '@/image/common_icons/arrow_down_icon.png';

import check_icon from '@/image/common_icons/check_icon.png';
import type_folder from '@/image/page_icons/type_folder.png';
import type_form from '@/image/page_icons/type_form.png';
import type_process from '@/image/page_icons/type_process.png';
import type_link from '@/image/page_icons/type_link.png';
import type_report from '@/image/page_icons/type_report.png';
import type_custompage from '@/image/page_icons/type_custompage.png';
import type_wiki from '@/image/page_icons/type_wiki.png';
import type_dashboard from '@/image/page_icons/type_dashboard.png';
import type_yunWangPan from '@/image/page_icons/type_yunWangPan.png';
import type_notes from '@/image/page_icons/type_notes.png';
import type_multiDimensionalTable from '@/image/page_icons/type_multiDimensionalTable.png';
import type_whiteboard from '@/image/page_icons/type_whiteboard.png';
import type_crud from '@/image/page_icons/type_crud.png';
import type_dataSetImport from '@/image/page_icons/type_dataSet_import.png';
import type_yudaopan from '@/image/page_icons/type_yudaopan.png';
import type_dataManage from '@/image/page_icons/type_dataManage.png';
import type_portlet from '@/image/page_icons/type_portlet.png';
import type_excelImport from '@/image/page_icons/type_excelImport.png';
// 接口api
import {
  javaApplication, //获取应用数据
  javaApplicationUpdate, //更新应用数据
  getApplicationPageAndClassList, //获取应用页面和分类
  createApplicationPageAndClass, //创建应用页面和分类
  updateApplicationPageAndClass, //更新应用页面和分类
  deleteApplicationPageAndClass //删除应用页面和分类\取消分类
} from '@/utils/api/api';

import set_icon from '@/image/common_icons/set_icon.png';
import release_icon from '@/image/common_icons/release_icon.png';
import myPeople_icon from '@/image/common_icons/myPeople_icon.png';
import tickProcessed_icon from '@/image/common_icons/tickProcessed_icon.png';
import createPlus_icon from '@/image/common_icons/createPlus_icon.png';
import sendShare_icon from '@/image/common_icons/sendShare_icon.png';

import approval_icon from '@/image/common_icons/approval_icon.png';

import ThemeSwitch from '@/component/ThemeSwitch';
import {Avatar} from 'amis-ui';
import CRUD from 'amis/lib/renderers/CRUD';

import {
  createReportObj,
  createIframeObj
} from '@/utils/schemaPageTemplate/createPageObjs';
import AppPublish from '@/views/AppPublish';
import {LogoutConfirmDialog} from '@/utils/schemaDataSet/LogoutConfirmDialog';
import {EditEntryIcon} from '@/utils/schemaDataSet/EditEntryIcon';

import AppSetting from '@/views/applySettings/index';
import FullScreen from '@/views/applySettings/fullScreen/index';
import {marginProp} from 'amis-ui/lib/components/virtual-list/constants';
import DataSet from '@/views/applySettings/component/dataSet/index';
import DataSource from '@/views/applySettings/component/dataSource/index';
import ControlDictionary from '@/views/manage/component/controlDictionary/index';
// 导入菜单管理组件
import MenuManager from '@/views/applyPage/component/menuManager/index';
// 导入页面管理组件
import PageManager from '@/views/applyPage/component/pageManager/index';

import CommonHeader from '@/views/components/CommonHeader';

declare global {
  interface Window {
    storeInstance: IMainStore;
  }
}

export default inject('store')(
  observer(function ({
    store,
    location,
    history,
    match
  }: {store: IMainStore} & RouteComponentProps<{
    appId: string;
    playType: string;
    form?: string;
    appSetMenu?: string;
    fullScreen?: string;
  }>) {
    // 获取应用数据
    const [applyData, setApplyData] = React.useState<any>({});
    // 是否是应用模板
    const [isAppTemplate, setIsAppTemplate] = React.useState<any>(false);

    const baseMenuList = [
      {
        id: 1,
        name: '菜单管理',
        path: `/app${match.params.appId}/admin/menuManager`,
        icon: 'fa fa-bars',
        children: [],
        system: true
      },
      {
        id: 2,
        name: ' 数据管理',
        path: `/app${match.params.appId}/admin/dataManage`,
        icon: 'fa fa-building',
        type: 2,
        children: [
          {
            id: 21,
            name: '控件字典管理',
            path: `/app${match.params.appId}/admin/controlDictionary`,
            icon: 'fas fa-hammer',
            children: [],
            system: true
          },
          {
            id: 22,
            name: '数据源管理',
            path: `/app${match.params.appId}/admin/dataSource`,
            icon: 'fa fa-database',
            children: [],
            system: true
          },
          {
            id: 23,
            name: '数据集管理',
            path: `/app${match.params.appId}/admin/dataSet`,
            icon: 'fa fa-table',
            children: [],
            system: true
          }
        ]
      }
    ];

    // 合并基础菜单和API返回的菜单（在应用模板的情况下）
    const [combinedMenuList, setCombinedMenuList] = React.useState<any[]>([]);

    // 添加一个生成企业 Logo 的函数
    const generateBusinessLogo = (name: string) => {
      if (!name) return '';
      // 获取企业名称的第一个字
      return name.charAt(0).toUpperCase();
    };

    // 引用菜单列表数据（分类+页面）
    const [appMenuList, setAppMenuList] = React.useState<any>([]);

    // 选中访问的页面数据
    const [checkedPageData, setCheckedPageData] = React.useState<any>(false);

    const handleJavaApplication = () => {
      const data = {
        id: Number(match.params.appId)
      };
      javaApplication(data).then(res => {
        if (res.code === 0) {
          setApplyData(res.data);
          // 判断是否是应用模板
          const appTemp = res.data.type == '2';
          setIsAppTemplate(appTemp);
          // if (res.data.projectId == null || res.data.isTemplate == 1) {
          //   setIsAppTemplate(true);
          // } else {
          //   setIsAppTemplate(false);
          // }
          handleGetApplicationPageAndClassList(undefined, appTemp);
          store.setApplyName(res.data.name);
          if (!res.data.logo) {
            res.data.logo = '';
          }
          store.setApplyLogo(res.data.logo);
        } else {
          setApplyData({});
          toast.error(res.msg);
        }
      });
    };

    const handlegetSystemComponentList = () => {
      let data = {
        pageNo: 1,
        pageSize: 999
      };
      getSystemComponentList(data).then((res: any) => {
        if (res.code == 0) {
          // 保存系统组件列表数据
          const componentConfigs = res.data.list || [];
          // 过滤出启用的组件配置
          const enabledConfigs = componentConfigs

          // 在编辑页面时使用这些组件配置
          if (checkedPageData && checkedPageData.pageType) {
            // 根据页面类型选择不同的渲染器
            let rendererType = 'page';
            if (
              checkedPageData.pageType == 1 ||
              checkedPageData.pageType == 2
            ) {
              rendererType = 'form';
            } else if (
              checkedPageData.pageType == 7 ||
              checkedPageData.pageType == 16
            ) {
              rendererType = 'dashboard';
            } else if (checkedPageData.pageType == 12) {
              rendererType = 'crud';
            } else if (checkedPageData.pageType == 17) {
              rendererType = 'portlet';
            }

            // 获取对应类型的渲染器列表
            const renderers = showRenderers(rendererType) || [];

            // 配置编辑器插件
            configureManagerEditorPlugin(renderers, enabledConfigs);
            console.log(
              `已配置编辑器插件，使用 ${rendererType} 类型的渲染器和 ${enabledConfigs.length} 个系统组件`
            );
          }
        } else {
          toast.error(res.msg);
        }
      });
    };
    // 获取应用分类与页面
    const handleGetApplicationPageAndClassList = (
      pageId?: number,
      isAppTemplate?: boolean
    ) => {
      let params = {
        applicationId: Number(match.params.appId),
        applicantOrBackend: 1
      };
      getApplicationPageAndClassList(params).then(res => {
        if (res.code === 0) {
          console.log('res.data', res.data);
          let data = res.data;
          setAppMenuList(data);

          // 如果是应用模板，合并基础菜单和API返回的菜单
          if (isAppTemplate) {
            const newCombinedMenuList = [...baseMenuList, ...data];
            setCombinedMenuList(newCombinedMenuList);
            console.log('combinedMenuList', newCombinedMenuList);
          }

          if (pageId) {
            // 在应用模板模式下，需要同时在combinedMenuList和appMenuList中查找页面
            if (isAppTemplate) {
              let arr = [];
              // 在combinedMenuList中查找
              arr = findPageInMenuList(pageId, [...baseMenuList, ...data]);
              if (arr.length > 0) {
                handleCheckedPageData(arr[0]);
              }
            } else {
              let arr = data.filter((item: any) => {
                return item.id == pageId;
              });
              if (arr.length > 0) {
                handleCheckedPageData(arr[0]);
              }
            }
          }
        } else {
          setAppMenuList([]);
          if (isAppTemplate) {
            setCombinedMenuList([...baseMenuList]);
          }
        }
      });
    };

    // 辅助函数：在菜单列表中递归查找指定ID的页面
    const findPageInMenuList = (pageId: number, menuList: any[]): any[] => {
      let result: any[] = [];

      // 遍历菜单列表
      for (const item of menuList) {
        // 检查当前项
        if (item.id == pageId) {
          result.push(item);
          break;
        }

        // 递归检查子菜单
        if (item.children && item.children.length > 0) {
          const found = findPageInMenuList(pageId, item.children);
          if (found.length > 0) {
            result = found;
            break;
          }
        }
      }

      return result;
    };

    // 转换菜单列表为导航链接格式
    const transformMenuListToNavLinks = (menuList: any[]): any[] => {
      return menuList.map((item: any) => {
        // 添加调试信息
        if (item.icon) {
          console.log('图标数据:', item.name, item.icon);
          if (item.icon.includes('<span') || item.icon.includes('&lt;span')) {
            console.log('发现包含span的图标:', item.name);
          }
        }

        const navItem: any = {
          key: item.id?.toString(),
          label: item.name,
          icon: item.icon,
          pageType: item.pageType,
          type: item.type,
          id: item.id,
          parentId: item.parentId,
          // 保留原始数据用于后续处理
          ...item
        };

        // 如果有子菜单，递归转换
        if (item.children && item.children.length > 0) {
          navItem.children = transformMenuListToNavLinks(item.children);
        }

        return navItem;
      });
    };

    // 临时的页面类型查找函数
    const findPageTypeByField = (fieldName: string, value: any) => {
      // 简单返回一个默认对象，避免类型错误
      return { imgSrc: '' };
    };

    // 渲染导航项内容
    const renderNavItemContent = (item: any) => {
      const pageTypeInfo = findPageTypeByField('typeNumber', item.pageType);

      return {
        type: 'flex',
        className: 'nav-item-page',
        style: {
          display: 'flex',
          alignItems: 'center',
          width: '100%',
          cursor: 'pointer'
        },
        body: {
          type: 'flex',
          className: 'nav-item-content',
          items: [
          {
            type: 'container',
            className: 'nav-item-icon',
            body: checkedPageData?.id == item.id ? {
              type: 'icon',
              icon: 'fa-solid fa-check',
              className: 'nav-item-check-icon'
            } : null
          },
          {
            type: 'container',
            className: 'nav-item-type-icon',
            body: item.icon && (item.icon.includes('<svg') || item.icon.includes('<span') || item.icon.includes('&lt;span')) ? (() => {
              console.log('使用tpl渲染图标:', item.name, item.icon);
              // 处理可能的HTML转义
              let iconHtml = item.icon;
              if (iconHtml.includes('&lt;') || iconHtml.includes('&gt;') || iconHtml.includes('&quot;')) {
                iconHtml = iconHtml
                  .replace(/&lt;/g, '<')
                  .replace(/&gt;/g, '>')
                  .replace(/&quot;/g, '"')
                  .replace(/&amp;/g, '&');
                console.log('转义后的图标HTML:', iconHtml);
              }
              return {
                type: 'tpl',
                tpl: iconHtml,
                className: 'nav-item-type-icon-svg'
              };
            })() : {
              type: 'image',
              src: pageTypeInfo?.imgSrc || '',
              className: 'nav-item-type-icon-img'
            }
          },
          {
            type: 'container',
            className: 'nav-item-name',
            body: (() => {
              const shouldShowInput = isRenamePageOrClass && renamePageOrClassItem?.id == item.id;
              console.log('渲染导航项名称 - item.id:', item.id, 'item.name:', item.name, 'isRenamePageOrClass:', isRenamePageOrClass, 'renamePageOrClassItem:', renamePageOrClassItem, 'shouldShowInput:', shouldShowInput);
              return shouldShowInput;
            })() ? {
              type: 'input-text',
              name: 'itemName',
              value: renamePageOrClassItem.name,
              className: 'nav-item-name-input',
              onBlur: () => renameClassOrPage(),
              onKeyDown: (e: any) => {
                if (e.key === 'Enter' || e.keyCode === 13) {
                  renameClassOrPage();
                }
              },
              onChange: (value: string) => {
                setRenamePageOrClassItem({
                  ...renamePageOrClassItem,
                  name: value
                });
              }
            } : {
              type: 'tpl',
              tpl: item.name || item.label,
              className: 'nav-item-name-text'
            }
          }
        ]
        }
      };
    };

    // 转换合并菜单列表为导航链接格式（应用模板）
    const transformCombinedMenuToNavLinks = (menuList: any[]): any[] => {
      return menuList.map((item: any) => {
        // 添加调试信息
        if (item.icon && item.icon.includes('<span')) {
          console.log('模板菜单发现包含span的图标:', item.name, item.icon);
        }

        const navItem: any = {
          key: item.id?.toString(),
          label: item.name,
          icon: item.icon,
          type: item.type,
          id: item.id,
          path: item.path,
          system: item.system,
          pageType: item.pageType,
          // 保留原始数据
          ...item
        };

        // 如果有子菜单，递归转换
        if (item.children && item.children.length > 0) {
          navItem.children = transformCombinedMenuToNavLinks(item.children);
        }

        return navItem;
      });
    };

    // 获取当前激活的菜单键值
    const getActiveMenuKey = (): string => {
      if (checkedPageData?.id) {
        return checkedPageData.id.toString();
      }

      // 检查系统菜单
      if (match.params.form) {
        const systemMenus = ['menuManager', 'controlDictionary', 'dataSource', 'dataSet'];
        for (const menu of systemMenus) {
          if (match.params.form.includes(menu)) {
            const foundItem = combinedMenuList.find(item =>
              item.path && item.path.includes(menu)
            );
            if (foundItem) {
              return foundItem.id.toString();
            }
          }
        }
      }

      return '';
    };

    // 渲染模板导航项内容
    const renderTemplateNavItemContent = (item: any) => {
      const hasChildren = item.type == 2 || (item.children && item.children.length > 0);
      const isSelected = getActiveMenuKey() === item.id?.toString();

      return {
        type: 'flex',
        className: `nav-item-template ${hasChildren ? 'nav-item-category' : 'nav-item-page'}`,
        items: [
          {
            type: 'container',
            className: hasChildren ? 'nav-item-expand-icon' : 'nav-item-icon',
            body: hasChildren ? {
              type: 'icon',
              icon: `fa-solid fa-caret-${openTypeIds.includes(item.id) ? 'down' : 'right'}`,
              className: 'nav-item-expand-icon-val'
            } : (isSelected ? {
              type: 'icon',
              icon: 'fa-solid fa-check',
              className: 'nav-item-check-icon'
            } : null)
          },
          {
            type: 'container',
            className: 'nav-item-type-icon',
            body: item.icon && (item.icon.includes('<svg') || item.icon.includes('<span') || item.icon.includes('&lt;span')) ? (() => {
              console.log('模板菜单使用tpl渲染图标:', item.name, item.icon);
              // 处理可能的HTML转义
              let iconHtml = item.icon;
              if (iconHtml.includes('&lt;') || iconHtml.includes('&gt;') || iconHtml.includes('&quot;')) {
                iconHtml = iconHtml
                  .replace(/&lt;/g, '<')
                  .replace(/&gt;/g, '>')
                  .replace(/&quot;/g, '"')
                  .replace(/&amp;/g, '&');
                console.log('模板菜单转义后的图标HTML:', iconHtml);
              }
              return {
                type: 'tpl',
                tpl: iconHtml,
                className: 'nav-item-type-icon-svg'
              };
            })() : {
              type: 'icon',
              icon: item.icon || (hasChildren ? 'fa fa-folder' : 'fa fa-file'),
              className: 'nav-item-type-icon-val'
            }
          },
          {
            type: 'tpl',
            tpl: item.name || item.label,
            className: 'nav-item-name-text'
          }
        ]
      };
    };

    // 处理模板菜单项点击
    const handleTemplateMenuItemClick = (item: any) => {
      const hasChildren = item.type == 2 || (item.children && item.children.length > 0);

      if (hasChildren) {
        // 如果有子菜单，切换展开/折叠状态
        handleSidebarTypeSwitch(item.id);
      } else {
        // 如果是普通页面项（type为1且有pageType），则调用handleCheckedPageData
        if (item.type === 1 && (item.pageType || !item.system)) {
          handleCheckedPageData(item);
          return;
        }

        // 如果没有子菜单，处理跳转逻辑
        if (item.path) {
          // 清除选中的页面数据，避免影响系统菜单的跳转
          setCheckedPageData(item);

          // 特殊处理一些系统菜单
          if (item.name === '控件字典管理') {
            history.push(
              `/app${match.params.appId}/${match.params.playType}/controlDictionary`
            );
          } else if (item.name === '数据源管理') {
            history.push(
              `/app${match.params.appId}/${match.params.playType}/dataSource`
            );
          } else if (item.name === '数据集管理') {
            history.push(
              `/app${match.params.appId}/${match.params.playType}/dataSet`
            );
          } else if (item.name === '菜单管理') {
            history.push(
              `/app${match.params.appId}/${match.params.playType}/menuManager`
            );
          } else {
            history.push(item.path);
          }
        }
      }
    };

    React.useEffect(() => {
      window.storeInstance = store;
    }, [store]);

    // 监听页面弹窗事件
    React.useEffect(() => {
      const handleShowPageDialog = (event: any) => {
        const { dialogType, pageType } = event.detail;
        console.log('接收到弹窗事件:', dialogType, pageType);

        // 跳转到页面类型选择界面
        setIsInitPageType(true);
        setCheckedPageData(false);
        history.push(`/app${match.params.appId}/${match.params.playType}`);

        // 延迟触发特定页面类型的弹窗
        setTimeout(() => {
          const triggerEvent = new CustomEvent('triggerPageTypeDialog', {
            detail: {
              dialogType,
              pageType
            }
          });
          window.dispatchEvent(triggerEvent);
        }, 100); // 等待 InitPageType 组件渲染完成
      };

      // 添加事件监听器
      window.addEventListener('showPageDialog', handleShowPageDialog);

      // 清理函数
      return () => {
        window.removeEventListener('showPageDialog', handleShowPageDialog);
      };
    }, []);

    // 添加监听 refreshMenuManager 事件，用于在菜单管理操作后刷新菜单列表
    React.useEffect(() => {
      const handleRefreshMenuManager = () => {
        console.log('接收到 refreshMenuManager 事件，刷新菜单列表');
        const isTemplate = applyData.projectId == null;
        handleGetApplicationPageAndClassList(undefined, isTemplate);
      };

      window.addEventListener('refreshMenuManager', handleRefreshMenuManager);

      // 组件卸载时移除事件监听
      return () => {
        window.removeEventListener(
          'refreshMenuManager',
          handleRefreshMenuManager
        );
      };
    }, [applyData]);

    React.useEffect(() => {
      if (appMenuList.length > 0 && !isAppTemplate) {
        if (match.params.form) {
          // 根据得到的页面id，获取初始选中的页面，显示对应内容
          let id = match.params.form.split('page')[1];
          if (id) {
            // 查找匹配的页面
            let foundPage = null;

            // 先在顶层查找
            let arr = appMenuList.filter((item: any) => {
              return item.type == 1 && item.id == id;
            });

            if (arr.length > 0) {
              foundPage = arr[0];
            } else {
              // 在子项中查找
              for (let item of appMenuList) {
                if (item.type != 1 && item.children) {
                  let childArr = item.children.filter((child: any) => {
                    return child.id == id;
                  });

                  if (childArr.length > 0) {
                    foundPage = childArr[0];
                    break;
                  }
                }
              }
            }

            if (foundPage) {
              // 强制更新页面数据，确保页面能够正确刷新
              setCheckedPageData(foundPage);
              setIsInitPageType(false);
            } else {
              history.push(
                `/app${match.params.appId}/${match.params.playType}`
              );
            }
          }
        } else {
          if (match.params.playType == 'workbench') {
            if (appMenuList[0].type == 1) {
              history.push(
                `/app${match.params.appId}/${match.params.playType}/page${appMenuList[0].id}`
              );
            } else {
              //崩溃修复
              if (
                appMenuList.length > 0 &&
                appMenuList[0].children &&
                appMenuList[0].children.length > 0
              ) {
                history.push(
                  `/app${match.params.appId}/${match.params.playType}/page${appMenuList[0].children[0].id}`
                );
              } else {
              }
            }
          }
        }
      }
    }, [appMenuList, match.params.form, isAppTemplate, checkedPageData?.id]);

    // 专门监听URL变化，确保路由切换时能立即响应
    React.useEffect(() => {
      // 当URL变化时，强制刷新页面数据
      if (match.params.form) {
        let id = match.params.form.split('page')[1];
        if (id) {
          // 查找匹配的页面
          let foundPage = null;

          // 在应用模板模式下，在combinedMenuList中查找
          if (isAppTemplate && combinedMenuList.length > 0) {
            foundPage = findPageInMenuList(Number(id), combinedMenuList)[0];
          } else if (appMenuList.length > 0) {
            // 在普通模式下，在appMenuList中查找
            // 先在顶层查找
            let arr = appMenuList.filter((item: any) => {
              return item.type == 1 && item.id == id;
            });

            if (arr.length > 0) {
              foundPage = arr[0];
            } else {
              // 在子项中查找
              for (let item of appMenuList) {
                if (item.type != 1 && item.children) {
                  let childArr = item.children.filter((child: any) => {
                    return child.id == id;
                  });

                  if (childArr.length > 0) {
                    foundPage = childArr[0];
                    break;
                  }
                }
              }
            }
          }

          if (foundPage) {
            // 强制更新页面数据，确保页面能够正确刷新
            setCheckedPageData(foundPage);
            setIsInitPageType(false);
          }
        }
      }
    }, [location.pathname, appMenuList, isAppTemplate, combinedMenuList]); // 添加依赖项确保菜单列表更新时也能响应

    // 添加history监听器，确保所有路由变化都能被捕获
    React.useEffect(() => {
      const unlisten = history.listen((location, action) => {
        // 当路由变化时，检查是否需要更新页面数据
        if (action === 'PUSH' || action === 'REPLACE') {
          const pathname = location.pathname;
          const pageMatch = pathname.match(/\/page(\d+)$/);

          if (pageMatch) {
            const pageId = pageMatch[1];
            let foundPage = null;

            // 在应用模板模式下，在combinedMenuList中查找
            if (isAppTemplate && combinedMenuList.length > 0) {
              foundPage = findPageInMenuList(
                Number(pageId),
                combinedMenuList
              )[0];
            } else if (appMenuList.length > 0) {
              // 在普通模式下，在appMenuList中查找
              // 先在顶层查找
              let arr = appMenuList.filter((item: any) => {
                return item.type == 1 && item.id == pageId;
              });

              if (arr.length > 0) {
                foundPage = arr[0];
              } else {
                // 在子项中查找
                for (let item of appMenuList) {
                  if (item.type != 1 && item.children) {
                    let childArr = item.children.filter((child: any) => {
                      return child.id == pageId;
                    });

                    if (childArr.length > 0) {
                      foundPage = childArr[0];
                      break;
                    }
                  }
                }
              }
            }

            if (foundPage) {
              // 强制更新页面数据，确保页面能够正确刷新
              setCheckedPageData(foundPage);
              setIsInitPageType(false);
            }
          }
        }
      });

      // 清理监听器
      return () => {
        unlisten();
      };
    }, [history, appMenuList, isAppTemplate, combinedMenuList]);

    React.useEffect(() => {
      if (match.params.appId) {
        handleJavaApplication();
        handlegetSystemComponentList();
        // handleGetApplicationPageAndClassList();
      }
    }, [match.params.appId]);

    // 处理baseMenuList的初始展开状态
    React.useEffect(() => {
      if (isAppTemplate) {
        // 默认展开数据管理菜单
        const dataManageMenu = baseMenuList.find(item => item.type === 2);
        if (dataManageMenu) {
          setOpenTypeIds([dataManageMenu.id]);
        }

        // 确保在应用模板模式下初始化合并菜单
        if (appMenuList.length > 0) {
          // 如果API已经返回了菜单数据，合并它们
          setCombinedMenuList([...baseMenuList, ...appMenuList]);
        } else {
          // 否则只使用基础菜单
          setCombinedMenuList([...baseMenuList]);
        }

        // 如果有路由参数form，则找到对应的菜单项并选中
        if (match.params.form) {
          // 在combinedMenuList中查找匹配的菜单项
          let foundMenuItem = findMenuItemByPath(
            combinedMenuList,
            match.params.form
          );
          if (foundMenuItem) {
            // 如果找到了菜单项，设置为选中状态
            setCheckedPageData(foundMenuItem);

            // 展开其所有父级菜单
            if (foundMenuItem.parentId) {
              // 查找父级菜单并添加到openTypeIds中
              const parentIds = getParentMenuIds(
                combinedMenuList,
                foundMenuItem.parentId
              );
              setOpenTypeIds(prev => [...new Set([...prev, ...parentIds])]);
            }
          }
        }
      }
    }, [isAppTemplate, match.params.form, appMenuList]);

    // 查找菜单项的函数
    const findMenuItemByPath = (menuList: any[], path: string): any => {
      for (const item of menuList) {
        // 检查当前菜单项
        if (item.path && item.path.includes(path)) {
          return item;
        }

        // 递归检查子菜单
        if (item.children && item.children.length > 0) {
          const found = findMenuItemByPath(item.children, path);
          if (found) return found;
        }
      }
      return null;
    };

    // 获取所有父级菜单ID的函数
    const getParentMenuIds = (
      menuList: any[],
      parentId: number | string
    ): (number | string)[] => {
      const result: (number | string)[] = [parentId];

      for (const item of menuList) {
        if (item.id === parentId) {
          // 如果找到了直接父级，检查它是否也有父级
          if (item.parentId) {
            result.push(...getParentMenuIds(menuList, item.parentId));
          }
          return result;
        }

        // 递归检查子菜单
        if (item.children && item.children.length > 0) {
          for (const child of item.children) {
            if (child.id === parentId) {
              // 找到了直接父级，将当前菜单项的ID添加到结果中
              result.push(item.id);
              // 继续向上查找
              if (item.parentId) {
                result.push(...getParentMenuIds(menuList, item.parentId));
              }
              return result;
            }
          }

          // 在当前菜单项的子菜单中递归查找
          const parentIds = getParentMenuIds(item.children, parentId);
          if (parentIds.length > 0) {
            result.push(item.id);
            if (item.parentId) {
              result.push(...getParentMenuIds(menuList, item.parentId));
            }
            return [...new Set([...result, ...parentIds])];
          }
        }
      }

      return result;
    };

    // 选中访问的页面或者分类数据
    const handleCheckedPageData = (item: any) => {
      // 如果是系统菜单项（baseMenuList中的菜单），特殊处理
      if (isAppTemplate && item.system) {
        setCheckedPageData(item);
        // 对于系统菜单，直接使用path进行导航
        if (item.path) {
          history.push(item.path);
        }
        return;
      }

      // 强制更新页面数据，确保页面能够正确刷新
      handleEdit(item.pageType);
      setCheckedPageData(item);
      //路由
      history.push(
        `/app${match.params.appId}/${match.params.playType}/page${item.id}`
      );
    };

    const handleEdit = (pageType: number): void => {
      // 根据页面类型选择不同的渲染器
      let rendererType = 'page';
      if (pageType == 1 || pageType == 2) {
        rendererType = 'form';
      } else if (pageType == 7 || pageType == 16) {
        rendererType = 'dashboard';
      } else if (pageType == 12) {
        rendererType = 'crud';
      } else if (pageType == 17) {
        rendererType = 'portlet';
      } else {
        rendererType = 'page';
      }
      console.log('rendererType', rendererType);
      let renderers: any = showRenderers(rendererType);

      // 重新获取系统组件列表并配置编辑器
      let data = {
        pageNo: 1,
        pageSize: 999
      };
      getSystemComponentList(data)
        .then((res: any) => {
          if (res.code == 0) {
            // 保存系统组件列表数据
            const componentConfigs = res.data.list || [];
            // 过滤出启用的组件配置
            const enabledConfigs = componentConfigs

            // 配置编辑器插件
            configureManagerEditorPlugin(renderers, enabledConfigs);
            console.log(
              `已配置编辑器插件，使用 ${rendererType} 类型的渲染器和 ${enabledConfigs.length} 个系统组件`
            );
          } else {
            // 如果获取失败，仍然使用渲染器配置编辑器，但不使用组件配置
            configureManagerEditorPlugin(renderers);
            toast.error(res.msg);
          }
        })
        .catch(() => {
          // 出错时降级处理：只配置渲染器，不使用组件配置
          configureManagerEditorPlugin(renderers);
        });
    };

    /* data 数据 */
    // 固定操作菜单
    const fixedPlayMenu = {
      pending: {
        name: '待我处理',
        path: `${
          process.env.NODE_ENV === 'development'
            ? 'http://localhost:88/#/workspace/unfinished'
            : 'https://javaddm.fjpipixia.com/wflow/#/workspace/unfinished'
        }`
      },
      transactors: {
        name: '我已处理',
        path: `${
          process.env.NODE_ENV === 'development'
            ? 'http://localhost:88/#/workspace/finished'
            : 'https://javaddm.fjpipixia.com/wflow/#/workspace/finished'
        }`
      },
      create: {
        name: '我创建的',
        path: `${
          process.env.NODE_ENV === 'development'
            ? 'http://localhost:88/#/workspace/submit'
            : 'https://javaddm.fjpipixia.com/wflow/#/workspace/submit'
        }`
      },
      ccusers: {
        name: '抄送我的',
        path: `${
          process.env.NODE_ENV === 'development'
            ? 'http://localhost:88/#/workspace/cc'
            : 'https://javaddm.fjpipixia.com/wflow/#/workspace/cc'
        }`
      }
    };

    // 编辑应用名称的item
    const [editApplyName, setEditApplyName] = React.useState<any>(false);
    // 是否显示编辑应用名称的input
    const [openApplyNameInput, setOpenApplyNameInput] =
      React.useState<any>(false);

    // 展开分类，数组保存分类的id
    const [openTypeIds, setOpenTypeIds] = React.useState<any[]>([]);

    //创建页面切换到选择页面类型页面
    const [isInitPageType, setIsInitPageType] = React.useState<any>(false);
    // 折叠页面导航菜单
    const [isCollapsePage, setIsCollapsePage] = React.useState<any>(false);

    // 页面搜索状态
    const [searchKeyword, setSearchKeyword] = React.useState<string>('');
    const [filteredMenuList, setFilteredMenuList] = React.useState<any[]>([]);
    const [searchExpanded, setSearchExpanded] = React.useState<boolean>(false);

    // 搜索过滤逻辑
    React.useEffect(() => {
      if (!searchKeyword.trim()) {
        setFilteredMenuList(appMenuList);
      } else {
        const filterMenuItems = (items: any[]): any[] => {
          const filtered: any[] = [];

          items.forEach(item => {
            // 检查当前项是否匹配搜索关键词
            const matchesKeyword = item.name?.toLowerCase().includes(searchKeyword.toLowerCase());

            if (item.children && item.children.length > 0) {
              // 递归过滤子项
              const filteredChildren = filterMenuItems(item.children);

              if (matchesKeyword || filteredChildren.length > 0) {
                // 如果当前项匹配或有匹配的子项，则包含此项
                filtered.push({
                  ...item,
                  children: filteredChildren
                });
              }
            } else if (matchesKeyword) {
              // 叶子节点且匹配关键词
              filtered.push(item);
            }
          });

          return filtered;
        };

        setFilteredMenuList(filterMenuItems(appMenuList));
      }
    }, [searchKeyword, appMenuList]);

    // 重名命页面名称/页面分类操作
    const [isRenamePageOrClass, setIsRenamePageOrClass] =
      React.useState<any>(false);
    // 重命名页面名称/页面分类的item
    const [renamePageOrClassItem, setRenamePageOrClassItem] =
      React.useState<any>(false);

    // 是否打开右键弹出层删除菜单
    const [rightPageMenu, setRighPagetMenu] = React.useState<any>(false);
    // 右键编辑页面菜单的数据item
    const [rightMenuItem, setRightMenuItem] = React.useState<any>(false);
    // 右键编辑菜单的类型: 1-页面 2-分类
    const [rightTypeMenu, setRightTypeMenu] = React.useState<any>(1);
    // 右键编辑页面菜单位置
    const [rightPageMenuPosition, setRightPageMenuPosition] =
      React.useState<any>({
        left: 0,
        top: 0
      });

    const [mianMenuBar, setMianMenuBar] = React.useState<any>(false);

    // 添加状态控制
    const [showLogoutDialog, setShowLogoutDialog] = React.useState(false);

    // 图标编辑相关状态
    const [showEditIconDialog, setShowEditIconDialog] = React.useState(false);
    const [editIconItem, setEditIconItem] = React.useState<any>(null);

    /* data 数据 end */

    /* methods 方法 */

    // 解析应用图标内容，提取SVG、颜色和背景色
    const parseAppIcon = (iconData: any) => {
      if (!iconData) {
        return {
          icon: '',
          iconColor: '#000000',
          iconBg: '#F5F7FA'
        };
      }

      // 如果是对象格式的图标
      if (typeof iconData === 'object' && iconData.svg) {
        return {
          icon: iconData.svg,
          iconColor: '#000000',
          iconBg: '#F5F7FA'
        };
      }

      // 如果是字符串格式的HTML
      if (typeof iconData === 'string') {
        // 如果是完整的span元素，需要解析其中的内容
        if (iconData.includes('<span')) {
          // 提取style属性中的颜色和背景色
          const colorMatch = iconData.match(/color:\s*([^;]+)/);
          const bgMatch = iconData.match(/background:\s*([^;]+)/);

          // 提取SVG内容或图标类名
          const svgMatch = iconData.match(/<svg[^>]*>.*?<\/svg>/);
          const iconMatch = iconData.match(/<i class="([^"]+)"/);

          return {
            icon: svgMatch ? svgMatch[0] : (iconMatch ? iconMatch[1] : ''),
            iconColor: colorMatch ? colorMatch[1] : '#000000',
            iconBg: bgMatch ? bgMatch[1] : '#F5F7FA'
          };
        }

        // 如果是纯SVG或图标类名，直接返回
        return {
          icon: iconData,
          iconColor: '#000000',
          iconBg: '#F5F7FA'
        };
      }

      // 默认返回
      return {
        icon: '',
        iconColor: '#000000',
        iconBg: '#F5F7FA'
      };
    };

    // 处理图标保存
    const handleIconSave = (values: any) => {
      console.log('处理页面图标保存:', values);

      if (!values || !Array.isArray(values) || values.length === 0) {
        console.log('无效的数据格式，取消更新');
        return;
      }

      const iconData = values[0];
      if (!iconData) {
        console.log('无图标数据，取消更新');
        return;
      }

      if (!editIconItem) {
        console.log('没有选中的页面项，取消更新');
        return;
      }

      // 清理图标数据，移除可能的引号
      let cleanIcon = iconData.icon || '';
      if (typeof cleanIcon === 'string') {
        // 移除可能的外层引号
        cleanIcon = cleanIcon.replace(/^['"]|['"]$/g, '').trim();
      }

      // 构建新的图标对象格式
      const newIconObject = {
        name: iconData.name || '自定义图标',
        id: iconData.id || `svg-${Date.now()}`,
        svg: cleanIcon
      };

      // 更新页面数据
      const updatedItem = {
        ...editIconItem,
        icon: newIconObject
      };

      console.log('准备更新页面图标:', updatedItem);

      // 调用更新API
      updateApplicationPageAndClass(updatedItem).then(res => {
        console.log('图标更新API响应:', res);
        if (res.code === 0) {
          // 刷新页面列表
          const isTemplate = applyData.projectId == null || applyData.isTemplate == 1;
          handleGetApplicationPageAndClassList(undefined, isTemplate);
          toast.success('图标设置成功');
        } else {
          toast.error(res.msg || '图标设置失败');
        }
      }).catch(error => {
        console.error('图标更新失败:', error);
        toast.error('图标设置失败');
      });

      // 关闭图标编辑对话框
      setShowEditIconDialog(false);
      setEditIconItem(null);
    };

    // 双击编辑应用名称
    const openEditApplyName = (e: any, name: any) => {
      if (e.detail === 2 && match.params.playType == 'admin') {
        console.log('双击编辑应用名称', name);
        setEditApplyName(name);
        setOpenApplyNameInput(true);
      }
    };
    // 更新保存应用名称
    const saveEditApplyName = () => {
      if (
        editApplyName.trim().length === 0 ||
        editApplyName.trim() === applyData.name
      ) {
        setEditApplyName(false);
        setOpenApplyNameInput(false);
        return;
      }
      let data = applyData;
      data.name = editApplyName;
      javaApplicationUpdate(data).then((res: any) => {
        if (res.code === 0) {
          handleJavaApplication();
          setEditApplyName(false);
          setOpenApplyNameInput(false);
          toast.success('修改成功');
        } else {
          toast.error(res.msg);
        }
      });
    };

    // 展开、收起分类事件
    const handleSidebarTypeSwitch = (id: string) => {
      if (openTypeIds.includes(id)) {
        setOpenTypeIds(openTypeIds.filter((item: string) => item !== id));
      } else {
        setOpenTypeIds([...openTypeIds, id]);
      }
    };

    // 创建新分类 - 检查该应用内创建新分类的名称是否重复
    const checkNewPagesTypeName = (name: string) => {
      let res: any = false;
      for (let i = 0; appMenuList.length > i; i++) {
        if (
          !appMenuList[i].page_class_id &&
          appMenuList[i].page_class_id !== ''
        ) {
          if (appMenuList[i].name === name) {
            res = true;
          }
        }
      }
      return res;
    };
    // 创建新分类
    const handleNewPagesType = (index: number) => {
      // if (checkedPageData.parentId) {
      //   toast.error('已有分类存在');
      //   return;
      // }
      let name = '';
      if (checkNewPagesTypeName(`分类 ${index}`)) {
        index++;
        handleNewPagesType(index);
        return;
      } else {
        name = `分类 ${index}`;
      }
      let data: any = {
        applicationId: Number(match.params.appId), //应用id
        applicantOrBackend: 1,
        parentId: 0, //父级id
        type: 2, //类型
        name //类名
      };
      createApplicationPageAndClass(data)
        .then((res: any) => {
          if (res.code === 0) {
            if (checkedPageData) {
              // 选中了页面的情况下
              handlePageMoveClass(res.data.id);
              // 且展开
              setOpenTypeIds([...openTypeIds, res.data.id]);
            } else {
              // 更新侧边菜单路由列表
              handleGetApplicationPageAndClassList();
              toast.success('创建成功');
            }
          } else {
            toast.error('创建失败:', res.msg);
          }
        })
        .catch((err: any) => {
          toast.error('创建失败 Err：', err);
        });
    };

    // 创建新页面
    const onSaveSuccess = (pageId: any) => {
      setIsInitPageType(false);
      handleGetApplicationPageAndClassList(pageId);
    };

    // 处理从MyNavPage传来的页面创建请求
    const handleCreatePageFromNav = (pageType: any) => {
      console.log('从导航创建页面:', pageType);

      // 根据页面类型执行相应的创建逻辑
      switch (pageType.typeName) {
        case 'form':
        case 'process':
          // 直接创建表单页面
          handleCreatePageDirect(pageType);
          break;
        default:
          // 其他类型暂时跳转到页面类型选择界面
          setIsInitPageType(true);
          setCheckedPageData(false);
          history.push(`/app${match.params.appId}/${match.params.playType}`);
      }
    };

    // 直接创建页面（用于表单类型）
    const handleCreatePageDirect = (pageType: any) => {
      const getTotalPages = () => {
        let res = 1;
        if (appMenuList) {
          for (let i = 0; appMenuList.length > i; i++) {
            if (appMenuList[i].children) {
              res += appMenuList[i].children.length;
            } else {
              res++;
            }
          }
        }
        return res;
      };

      let data: any = {
        url: '',
        applicationId: match.params.appId,
        applicantOrBackend: 1,
        pageType: pageType.typeNumber,
        parentId: 0,
        name: `页面 ${getTotalPages()}`,
        type: 1
      };

      // 调用创建页面API
      createApplicationPageAndClass(data).then((res: any) => {
        if (res.code == 0) {
          console.log('页面创建成功:', res);
          onSaveSuccess(res.data.id);
        } else {
          toast.error('创建失败 ' + res.msg);
        }
      });
    };

    // 取消分类
    const cancelClass = (item: any) => {
      if (item.type != 2) {
        toast.error('当前不是分类');
        return;
      }
      let data = {
        id: item.id,
        type: item.type,
        operateType: 1
      };
      deleteApplicationPageAndClass(data).then((res: any) => {
        if (res.code === 0) {
          setRighPagetMenu(false);
          handleGetApplicationPageAndClassList();
          toast.success('删除成功');
        } else {
          toast.error(res.msg);
        }
      });
    };

    // 删除页面/分类（右键弹出层删除菜单）
    const delClassOrPage = (item: any) => {
      let data = {
        id: item.id,
        type: item.type,
        operateType: 2
      };
      deleteApplicationPageAndClass(data).then((res: any) => {
        if (res.code === 0) {
          setRighPagetMenu(false);
          handleGetApplicationPageAndClassList();
          toast.success('删除成功');
        } else {
          toast.error(res.msg);
        }
      });
    };
    // 重命名页面/分类（双击）
    // 编辑分类名称 - 双击开启编辑分类名称 input
    const openEditTypeName = (e: any, item: any) => {
      // e.stopPropagation();
      if (match.params.playType != 'admin') return;
      if (e.detail === 2) {
        e.stopPropagation();
        setIsRenamePageOrClass(true);
        setRightMenuItem(item);
        setRenamePageOrClassItem(item);
      }
    };

    // 重命名页面/分类（右键弹出层删除菜单）
    const handleReName = () => {
      console.log('开始重命名:', rightMenuItem);
      setIsRenamePageOrClass(true);
      setRighPagetMenu(false);
      setRenamePageOrClassItem(rightMenuItem);
      console.log('重命名状态设置完成 - isRenamePageOrClass: true, renamePageOrClassItem:', rightMenuItem);
    };

    // 使用新名称更新页面/分类信息
    const renameClassOrPageWithNewName = (updatedItem: any) => {
      console.log('renameClassOrPageWithNewName 被调用');
      console.log('updatedItem.name:', updatedItem.name);
      console.log('rightMenuItem.name:', rightMenuItem.name);

      if (updatedItem.name === rightMenuItem.name) {
        console.log('名称没有变化，取消重命名');
        setIsRenamePageOrClass(false);
        setRightMenuItem(false);
        setRenamePageOrClassItem(false);
        return;
      }

      console.log('名称已变化，调用API更新');
      console.log('发送的数据:', updatedItem);

      updateApplicationPageAndClass(updatedItem).then(res => {
        console.log('API响应:', res);
        if (res.code === 0) {
          console.log('API调用成功，立即重置状态');
          // 立即重置状态
          setIsRenamePageOrClass(false);
          setRightMenuItem(false);
          setRenamePageOrClassItem(false);

          // 手动移除所有输入框，恢复原始显示
          const inputs = document.querySelectorAll('.nav-item-name-input');
          inputs.forEach(input => {
            const container = input.parentElement;
            if (container) {
              // 创建新的文本元素
              const textElement = document.createElement('span');
              textElement.className = 'nav-item-name-text';
              textElement.textContent = updatedItem.name; // 使用新名称
              container.replaceChild(textElement, input);
            }
          });

          handleGetApplicationPageAndClassList();
          toast.success('已修改');
        } else {
          toast.error(res.msg);
          // API失败时也要重置状态
          setIsRenamePageOrClass(false);
          setRightMenuItem(false);
          setRenamePageOrClassItem(false);
        }
      }).catch(error => {
        console.error('API调用失败:', error);
        // 出错时也要重置状态
        setIsRenamePageOrClass(false);
        setRightMenuItem(false);
        setRenamePageOrClassItem(false);
      });
    };

    // 更新页面/分类信息-编辑页面、分类名称（重命名）- 保留原函数以兼容其他调用
    const renameClassOrPage = () => {
      console.log('renameClassOrPage 被调用');
      console.log('renamePageOrClassItem.name:', renamePageOrClassItem.name);
      console.log('rightMenuItem.name:', rightMenuItem.name);

      if (renamePageOrClassItem.name == rightMenuItem.name) {
        console.log('名称没有变化，取消重命名');
        setIsRenamePageOrClass(false);
        setRightMenuItem(false);
        setRenamePageOrClassItem(false);
        return;
      }

      console.log('名称已变化，调用API更新');
      let data = renamePageOrClassItem;
      console.log('发送的数据:', data);

      updateApplicationPageAndClass(data).then(res => {
        console.log('API响应:', res);
        if (res.code === 0) {
          handleGetApplicationPageAndClassList();
          toast.success('已修改');
        } else {
          toast.error(res.msg);
        }
        setTimeout(() => {
          console.log('重置重命名状态');
          setIsRenamePageOrClass(false);
          setRightMenuItem(false);
          setRenamePageOrClassItem(false);
        }, 300);
      }).catch(error => {
        console.error('API调用失败:', error);
        setTimeout(() => {
          setIsRenamePageOrClass(false);
          setRightMenuItem(false);
          setRenamePageOrClassItem(false);
        }, 300);
      });
    };

    // 更新页面信息之分类创建成功后，移入对应创建的分类
    const handlePageMoveClass = (parentId: number) => {
      // 选中的分类id
      let data = checkedPageData;
      data.parentId = parentId;
      updateApplicationPageAndClass(data).then(res => {
        if (res.code === 0) {
          handleGetApplicationPageAndClassList();
          toast.success('创建成功');
        } else {
          toast.error(res.msg);
        }
      });
    };

    // 禁止页面右键事件-ID为contextmenu的模块
    const preventDefault = () => {
      document.addEventListener('contextmenu', function (e) {
        e.preventDefault();
      });
    };
    // 打开右键弹出层菜单
    const openRighPageMenu = (e: any, item: any) => {
      // 只有在admin模式下才允许右键操作
      if (match.params.playType != 'admin') {
        return;
      }

      if (e.button === 2) {
        e.preventDefault();
        const rect = document.documentElement.getBoundingClientRect();
        const windowHeight = rect.bottom - rect.top;
        const menuRect: any = document
          .getElementById('customContextMenu')
          ?.getBoundingClientRect();
        const menuHeight: any = menuRect?.bottom - menuRect?.top;
        // 计算鼠标右击位置是否超过页面可视高度一半
        const isAboveHalf = e.clientY < windowHeight / 2;

        if (isAboveHalf) {
          // 如果在一半以上，模块最高处为鼠标右击位置
          setRightPageMenuPosition({
            x: e.clientX,
            y: e.clientY
          });
        } else {
          // 如果在一半以下，模块最低部为鼠标右击位置
          setRightPageMenuPosition({
            x: e.clientX,
            y: e.clientY - (menuHeight || 0)
          });
        }
        setRighPagetMenu(true);
        setRightMenuItem(item);
        setRightTypeMenu(item.type);
        console.log('右键菜单设置 - item.type:', item.type, 'item.name:', item.name, 'item:', item);
      }
    };

    // 退出登录
    const LogOut = () => {
      history.push('/login'); // 先跳转
      setTimeout(() => {
        // 延迟清除状态
        store.setAccessToken('');
        store.setTenantId('');
        store.setUserInfo(false);
      }, 100);
    };

    React.useEffect(() => {
      if (rightPageMenu) {
        preventDefault();
        document.addEventListener('click', function (e) {
          var customContextMenu: any =
            document.getElementById('customContextMenu');
          if (customContextMenu && !customContextMenu.contains(e.target)) {
            // 关闭右键弹出层菜单
            setRighPagetMenu(false);
            // 右键编辑页面菜单的数据item
            setRightMenuItem(false);
            // 重命名数据item
            setRenamePageOrClassItem(false);
          }
        });
      }
    }, [rightPageMenu]);

    // 存储事件监听器的引用，以便后续移除
    const contextMenuListeners = React.useRef<Map<Element, (e: MouseEvent) => void>>(new Map());

    // 使用 MutationObserver 监听导航项的DOM变化，并添加右键事件
    React.useEffect(() => {
      if (match.params.playType !== 'admin') return;

      const addContextMenuToNavItems = () => {
        // 首先移除所有现有的事件监听器
        contextMenuListeners.current.forEach((listener, element) => {
          element.removeEventListener('contextmenu', listener);
        });
        contextMenuListeners.current.clear();

        // 查找所有导航项（包括页面和分类）
        const navItems = document.querySelectorAll('.nav-item-page, .nav-item-wrapper, .cxd-Nav-Menu-item, .cxd-Nav-Menu-submenu-title, .cxd-Nav-Menu-submenu > .cxd-Nav-Menu-item-link');

        navItems.forEach((element: any) => {
          // 尝试从不同的地方获取item数据
          let item: any = null;

          // 方法1: 从元素的文本内容推断
          let nameElement = element.querySelector('.nav-item-name-text, .cxd-Nav-Menu-item-label, .cxd-Nav-Menu-item-link-text, .cxd-Nav-Menu-submenu-title-text');
          let itemName = '';

          if (nameElement) {
            itemName = (nameElement.textContent || nameElement.innerText || '').trim();
          } else {
            // 如果找不到特定的文本元素，尝试使用元素本身的文本内容
            itemName = (element.textContent || element.innerText || '').trim();
            // 清理文本，只保留第一行（分类名称）
            itemName = itemName.split('\n')[0].trim();
          }

          if (itemName) {
            // 在菜单列表中查找匹配的项
            const findItemByName = (menuList: any[], name: string): any => {
              for (const menuItem of menuList) {
                if (menuItem.name === name || menuItem.label === name) {
                  return menuItem;
                }
                if (menuItem.children && menuItem.children.length > 0) {
                  const found = findItemByName(menuItem.children, name);
                  if (found) return found;
                }
              }
              return null;
            };

            item = findItemByName(appMenuList, itemName);
            console.log('查找导航项:', itemName, '找到:', item);
          }

          // 方法2: 从父元素的data属性获取
          if (!item) {
            const parentNav = element.closest('[data-key], [data-id]');
            if (parentNav) {
              const key = parentNav.getAttribute('data-key') || parentNav.getAttribute('data-id');
              if (key) {
                const findItemById = (menuList: any[], id: string): any => {
                  for (const menuItem of menuList) {
                    if (menuItem.id?.toString() === id || menuItem.key === id) {
                      return menuItem;
                    }
                    if (menuItem.children && menuItem.children.length > 0) {
                      const found = findItemById(menuItem.children, id);
                      if (found) return found;
                    }
                  }
                  return null;
                };

                item = findItemById(appMenuList, key);
              }
            }
          }

          // 如果找到了item数据，添加右键事件
          if (item) {
            // 创建事件监听器函数
            const contextMenuHandler = (e: MouseEvent) => {
              e.preventDefault();
              console.log('右键点击导航项:', item);
              openRighPageMenu(e, item);
              return false;
            };

            // 添加事件监听器
            element.addEventListener('contextmenu', contextMenuHandler);

            // 存储监听器引用以便后续移除
            contextMenuListeners.current.set(element, contextMenuHandler);

            console.log('为导航项添加右键事件:', item.name, 'ID:', item.id);
          }
        });
      };

      // 初始添加
      setTimeout(addContextMenuToNavItems, 500);

      // 监听DOM变化
      const observer = new MutationObserver((mutations) => {
        let shouldUpdate = false;
        mutations.forEach((mutation) => {
          // 检查是否有节点添加或删除
          if (mutation.type === 'childList' && (mutation.addedNodes.length > 0 || mutation.removedNodes.length > 0)) {
            shouldUpdate = true;
          }
          // 检查是否有属性变化（可能影响导航项）
          if (mutation.type === 'attributes') {
            shouldUpdate = true;
          }
        });

        if (shouldUpdate) {
          console.log('检测到DOM变化，重新绑定右键事件');
          setTimeout(addContextMenuToNavItems, 200);
        }
      });

      // 开始观察侧边栏的变化
      const asideContainer = document.querySelector('.asidePagesClass');
      if (asideContainer) {
        observer.observe(asideContainer, {
          childList: true,
          subtree: true
        });
      }

      return () => {
        observer.disconnect();
      };
    }, [match.params.playType]); // 移除 appMenuList 依赖，避免频繁重新绑定

    // 单独监听 appMenuList 的变化，重新绑定事件
    React.useEffect(() => {
      if (match.params.playType !== 'admin') return;

      console.log('菜单列表更新，重新绑定右键事件');

      // 延迟重新绑定事件
      setTimeout(() => {
        // 首先移除所有现有的事件监听器
        contextMenuListeners.current.forEach((listener, element) => {
          element.removeEventListener('contextmenu', listener);
        });
        contextMenuListeners.current.clear();

        const navItems = document.querySelectorAll('.nav-item-page, .nav-item-wrapper, .cxd-Nav-Menu-item, .cxd-Nav-Menu-submenu-title, .cxd-Nav-Menu-submenu > .cxd-Nav-Menu-item-link');

        navItems.forEach((element: any) => {
          let item: any = null;
          let nameElement = element.querySelector('.nav-item-name-text, .cxd-Nav-Menu-item-label, .cxd-Nav-Menu-item-link-text, .cxd-Nav-Menu-submenu-title-text');
          let itemName = '';

          if (nameElement) {
            itemName = (nameElement.textContent || nameElement.innerText || '').trim();
          } else {
            itemName = (element.textContent || element.innerText || '').trim();
            itemName = itemName.split('\n')[0].trim();
          }

          if (itemName) {
            const findItemByName = (menuList: any[], name: string): any => {
              for (const menuItem of menuList) {
                if (menuItem.name === name || menuItem.label === name) {
                  return menuItem;
                }
                if (menuItem.children && menuItem.children.length > 0) {
                  const found = findItemByName(menuItem.children, name);
                  if (found) return found;
                }
              }
              return null;
            };

            item = findItemByName(appMenuList, itemName);
          }

          if (item) {
            // 创建事件监听器函数
            const contextMenuHandler = (e: MouseEvent) => {
              e.preventDefault();
              console.log('右键点击导航项 (重新绑定):', item);

              // 记录被右键点击的元素
              lastRightClickedElement.current = element;
              console.log('记录右键点击的元素:', element);

              openRighPageMenu(e, item);
              return false;
            };

            // 添加事件监听器
            element.addEventListener('contextmenu', contextMenuHandler);

            // 存储监听器引用以便后续移除
            contextMenuListeners.current.set(element, contextMenuHandler);

            console.log('为导航项重新绑定右键事件:', item.name, 'ID:', item.id);
          }
        });
      }, 300);
    }, [appMenuList]);

    // 存储最后一次右键点击的元素
    const lastRightClickedElement = React.useRef<Element | null>(null);

    // 监听重命名状态变化，手动更新DOM
    React.useEffect(() => {
      if (isRenamePageOrClass && renamePageOrClassItem) {
        console.log('重命名状态变化，尝试手动更新DOM');
        console.log('目标项:', renamePageOrClassItem);
        console.log('最后右键点击的元素:', lastRightClickedElement.current);

        // 延迟执行，确保DOM已经渲染
        setTimeout(() => {
          // 方法1: 如果有记录最后右键点击的元素，直接使用它
          if (lastRightClickedElement.current) {
            console.log('使用记录的右键点击元素');
            const targetElement = lastRightClickedElement.current;

            // 在这个元素中查找名称元素
            let nameElement = targetElement.querySelector('.nav-item-name-text, .cxd-Nav-Menu-item-label, [class*="label"], [class*="name"]');

            if (nameElement) {
              console.log('在右键点击的元素中找到名称元素:', nameElement);

              // 直接为这个元素创建输入框
              const input = document.createElement('input');
              input.type = 'text';
              input.value = renamePageOrClassItem.name;
              input.className = 'nav-item-name-input';
              input.style.cssText = `
                width: 100%;
                padding: 4px 8px;
                border: 1px solid #ccc;
                border-radius: 4px;
                font-size: 12px;
                background: white;
              `;

              // 替换文本元素
              const nameContainer = nameElement.parentElement;
              if (nameContainer) {
                nameContainer.replaceChild(input, nameElement);

                // 聚焦并选中文本
                input.focus();
                input.select();

                // 保存原始的名称元素，用于恢复
                const originalNameElement = nameElement.cloneNode(true);

                // 添加事件监听器
                const restoreOriginalText = () => {
                  console.log('恢复原始文本');
                  const nameContainer = input.parentElement;
                  if (nameContainer) {
                    nameContainer.replaceChild(originalNameElement, input);
                  }
                };

                const handleFinishRename = () => {
                  console.log('handleFinishRename 被调用');
                  const newName = input.value.trim();
                  console.log('新名称:', newName, '原名称:', renamePageOrClassItem.name);

                  if (newName && newName !== renamePageOrClassItem.name) {
                    // 直接调用重命名函数，传递新名称
                    console.log('名称已变化，调用重命名函数');
                    const updatedItem = {
                      ...renamePageOrClassItem,
                      name: newName
                    };
                    renameClassOrPageWithNewName(updatedItem);
                  } else {
                    // 名称没有变化，恢复原来的文本并取消重命名
                    console.log('名称没有变化，恢复原来的文本');
                    restoreOriginalText();
                    setIsRenamePageOrClass(false);
                    setRightMenuItem(false);
                    setRenamePageOrClassItem(false);
                  }
                };

                const handleCancel = () => {
                  console.log('取消重命名');
                  restoreOriginalText();
                  setIsRenamePageOrClass(false);
                  setRenamePageOrClassItem(false);
                };

                // 回车或失去焦点时完成重命名
                input.addEventListener('keydown', (e) => {
                  console.log('键盘事件:', e.key);
                  if (e.key === 'Enter') {
                    console.log('按下 Enter 键');
                    e.preventDefault();
                    handleFinishRename();
                  } else if (e.key === 'Escape') {
                    console.log('按下 Escape 键');
                    e.preventDefault();
                    handleCancel();
                  }
                });

                input.addEventListener('blur', () => {
                  console.log('输入框失去焦点');
                  handleFinishRename();
                });

                console.log('事件监听器已绑定到输入框');
              }
            } else {
              console.log('在右键点击的元素中未找到名称元素');
            }
          } else {
            console.log('没有记录的右键点击元素，使用原来的查找方法');
            // 原来的查找方法作为备用 - 暂时跳过，先测试新方法
          }
        }, 100);
      }
    }, [isRenamePageOrClass, renamePageOrClassItem]);


    /* methods 方法 end */

    /* created 初始化 */

    /* created 初始化 end */

    const pageTypeList = [
      {
        name: '新建普通表单',
        typeNumber: 1,
        typeName: 'form',
        imgSrc: type_form
      },
      {
        name: '新建流程表单',
        typeNumber: 2,
        typeName: 'process',
        imgSrc: type_process
      },
      {
        name: 'Excel导入',
        typeNumber: 3,
        typeName: 'excelImport',
        imgSrc: type_excelImport
      },
      {
        name: '添加外部链接',
        typeNumber: 4,
        typeName: 'link',
        imgSrc: type_link
      },
      {
        name: '新建自定义页面',
        typeNumber: 5,
        typeName: 'custompage',
        imgSrc: type_custompage
      },

      {
        name: 'amis报表',
        typeNumber: 7,
        typeName: 'dashboard',
        imgSrc: type_dashboard
      },

      // {
      //   name: 'CRUD',
      //   typeNumber: 12,
      //   typeName: 'CRUD',
      //   imgSrc: type_crud
      // },
      {
        name: '数据源导入',
        typeNumber: 13,
        typeName: 'dataSetImport',
        imgSrc: type_dataSetImport
      },
      {
        name: '芋道网盘',
        typeNumber: 14,
        typeName: 'yudaopan',
        imgSrc: type_yudaopan
      },
      {
        name: '门户',
        typeNumber: 17,
        typeName: 'portlet',
        imgSrc: type_portlet
      }
    ];

    // 首先定义页面类型的接口
    interface PageType {
      name: string;
      typeNumber: number;
      typeName: string;
      imgSrc: any;
    }





    /* page 页面构建渲染 */
    // 工作台
    const renderAside = () => {
      return (
        <>
          {/* 其他操作栏 */}
          {/* {match.params.playType == 'admin' && (
            <div className="headActionBar">
              <div
                className="headActionBar-item"
                onClick={() => {
                  setCheckedPageData(false);
                  setIsInitPageType(false);
                  history.push(
                    `/app${match.params.appId}/${match.params.playType}/appSetting/basicSetting`
                  );
                }}
              >
                <i className="fa-solid fa-gear headActionBar-item-icon"></i>
                <div className="headActionBar-item-name">应用设置</div>
              </div>
              <div
                className={
                  match.params.form == 'appPublish'
                    ? 'headActionBar-item item-click'
                    : 'headActionBar-item'
                }
                onClick={() => {
                  setCheckedPageData(false);
                  setIsInitPageType(false);
                  history.push(
                    `/app${match.params.appId}/${match.params.playType}/appPublish`
                  );
                }}
              >
                <i className="fa-solid fa-upload headActionBar-item-icon"></i>
                <div className="headActionBar-item-name">应用发布</div>
              </div>
              <div
                className={
                  match.params.form == 'approval'
                    ? 'headActionBar-item item-click'
                    : 'headActionBar-item'
                }
                onClick={() => {
                  setCheckedPageData(false);
                  setIsInitPageType(false);
                  history.push(
                    `/app${match.params.appId}/${match.params.playType}/approval`
                  );
                }}
              >
                <i className="fa-solid fa-stamp headActionBar-item-icon"></i>
                <div className="headActionBar-item-name">审批</div>
              </div>
              <div className="headActionBar-line"></div>
            </div>
          )} */}
          {/* 页面导航头部功能已合并到导航内容中，移除重复的头部组件 */}

          {/* 缩起状态下的固定展开按钮 */}
          {isCollapsePage && match.params.playType == 'admin' && !isAppTemplate && (
            <div
              className="fixed-expand-btn"
              onClick={() => setIsCollapsePage(false)}
              title="展开导航"
            >
              <svg
                viewBox="0 0 16 16"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path strokeLinecap="round" strokeLinejoin="round" d="m2.35 5.497 5.657 5.657 5.657-5.657"/>
              </svg>
            </div>
          )}

          {/* 页面导航 type 1页面 2分类*/}
          {!isAppTemplate && !isCollapsePage && (
            <>
              {console.log('渲染页面导航内容，searchExpanded:', searchExpanded)}
              <AMISRenderer
                schema={{
                type: 'my-nav-page',
                stacked: true,
                className: `pages-nav-content ${isCollapsePage ? 'collapsed' : ''}`,
                // 添加头部配置到导航内容中
                header: {
                  enable: true,
                  showTitle: !isCollapsePage,
                  title: '页面',
                  showSearch: !isCollapsePage,
                  showAddNav: !isCollapsePage,
                  showCollapse: true, // 缩起按钮始终显示
                  className: 'page-nav-header-bar',
                  collapsed: isCollapsePage
                },
                // 启用导航项操作栏
                enableItemActions: true,
                // 配置导航项悬浮操作按钮
                itemActions: [
                  {
                    type: 'dropdown-button',
                    icon: 'fa fa-cog',
                    level: 'link',
                    size: 'xs',
                    className: 'nav-item-settings-btn',
                    hideCaret: true,
                    closeOnClick: true,
                    trigger: 'click', // 点击触发（hover在amis中可能不稳定）
                    buttons: [
                      {
                        type: 'button',
                        label: '修改名称',
                        actionType: 'custom',
                        className: 'nav-menu-item',
                        onAction: (e: any, action: any, data: any) => {
                          console.log('修改名称 - data:', data);

                          // 需要从菜单列表中找到正确格式的数据
                          let correctPageData: any = null;

                          // 查找函数
                          const findItemById = (menuList: any[], targetId: number): any => {
                            for (let item of menuList) {
                              if (item.id == targetId) {
                                return item;
                              }
                              if (item.children && item.children.length > 0) {
                                const found = findItemById(item.children, targetId);
                                if (found) return found;
                              }
                            }
                            return null;
                          };

                          // 在应用模板模式下查找
                          if (isAppTemplate && combinedMenuList.length > 0) {
                            correctPageData = findItemById(combinedMenuList, data.id);
                          } else if (appMenuList.length > 0) {
                            correctPageData = findItemById(appMenuList, data.id);
                          }

                          console.log('找到的正确数据:', correctPageData);

                          if (correctPageData) {
                            // 查找对应的DOM元素
                            const navItems = document.querySelectorAll('.cxd-Nav-Menu-item');
                            let targetElement = null;

                            navItems.forEach((element: any) => {
                              const nameElement = element.querySelector('.cxd-Nav-Menu-item-label');
                              if (nameElement && nameElement.textContent?.trim() === correctPageData.name) {
                                targetElement = element;
                              }
                            });

                            console.log('找到的目标DOM元素:', targetElement);

                            if (targetElement) {
                              // 设置最后右键点击的元素
                              lastRightClickedElement.current = targetElement;
                            }

                            // 调用重命名功能
                            setRenamePageOrClassItem(correctPageData);
                            setIsRenamePageOrClass(true);
                            setRightMenuItem(correctPageData);
                          } else {
                            toast.error('未找到页面数据，无法重命名');
                          }
                        }
                      },
                      {
                        type: 'button',
                        label: '设置图标',
                        actionType: 'custom',
                        className: 'nav-menu-item',
                        onAction: (e: any, action: any, data: any) => {
                          console.log('设置图标 - data:', data);
                          // 设置当前要编辑图标的页面项
                          setEditIconItem(data);
                          setShowEditIconDialog(true);
                        }
                      },
                      {
                        type: 'button',
                        label: '访问',
                        actionType: 'custom',
                        className: 'nav-menu-item',
                        onAction: (e: any, action: any, data: any) => {
                          console.log('访问页面 - data:', data);
                          console.log('页面ID:', data.id, '类型:', data.type);
                          console.log('当前路由参数:', match.params);
                          console.log('当前选中的页面:', checkedPageData);

                          if (data && data.id) {
                            // 构建目标路由
                            const targetRoute = `/app${match.params.appId}/${match.params.playType}/page${data.id}`;
                            console.log('目标路由:', targetRoute);

                            // 从 appMenuList 中查找正确格式的页面数据
                            let correctPageData = null;

                            // 查找函数
                            const findPageById = (menuList: any[], targetId: number): any => {
                              for (let item of menuList) {
                                if (item.type === 1 && item.id == targetId) {
                                  return item;
                                }
                                if (item.children && item.children.length > 0) {
                                  const found = findPageById(item.children, targetId);
                                  if (found) return found;
                                }
                              }
                              return null;
                            };

                            // 在应用模板模式下查找
                            if (isAppTemplate && combinedMenuList.length > 0) {
                              correctPageData = findPageById(combinedMenuList, data.id);
                            } else if (appMenuList.length > 0) {
                              correctPageData = findPageById(appMenuList, data.id);
                            }

                            console.log('从菜单列表中找到的正确页面数据:', correctPageData);

                            if (correctPageData) {
                              // 使用正确格式的页面数据
                              handleCheckedPageData(correctPageData);
                            } else {
                              // 如果没找到，提示用户
                              console.log('未找到页面数据');
                              toast.error('页面数据不存在，无法访问');
                            }
                          }
                        }
                      },
                      {
                        type: 'button',
                        label: '删除',
                        actionType: 'custom',
                        className: 'nav-menu-item nav-menu-item-danger',
                        confirmText: '确定要删除这个页面吗？',
                        onAction: (e: any, action: any, data: any) => {
                          console.log('删除页面 - data:', data);
                          // 调用删除功能
                          if (data && data.id) {
                            delClassOrPage(data);
                          }
                        }
                      }
                    ]
                  }
                ],
                links: transformMenuListToNavLinks(searchKeyword ? filteredMenuList : appMenuList),
                draggable: match.params.playType === 'admin',
                searchable: searchExpanded, // 使用搜索展开状态
                searchConfig: {
                  placeholder: '搜索页面...',
                  className: 'page-nav-search'
                },
                collapsed: isCollapsePage,
                onSelect: (item: any) => {
                  if (item.pageType) {
                    handleCheckedPageData(item);
                  }
                },
                activeKey: checkedPageData?.id?.toString(),
                itemRender: (item: any) => renderNavItemContent(item),
                onContextMenu: match.params.playType === 'admin' ? (e: any, item: any) => {
                  openRighPageMenu(e, item);
                } : undefined,
                onCreatePage: (pageType: any) => {
                  console.log('导航内容创建页面:', pageType);
                  handleCreatePageFromNav(pageType);
                },
                onAddCategory: () => handleNewPagesType(1),
                onSearch: (keyword: string) => {
                  console.log('页面导航内容搜索:', keyword);
                  setSearchKeyword(keyword);
                },
                // 添加头部操作回调
                onHeaderAction: (action: string, data?: any) => {
                  switch (action) {
                    case 'search':
                      // 搜索功能处理
                      if (data && typeof data.keyword === 'string') {
                        // 搜索输入变化 - 更新搜索关键词
                        console.log('导航内容接收到搜索输入:', data.keyword);
                        setSearchKeyword(data.keyword);
                      } else if (data && typeof data.expanded === 'boolean') {
                        // 搜索按钮点击 - 切换搜索展开状态
                        console.log('导航内容接收到搜索展开状态:', data.expanded);
                        setSearchExpanded(data.expanded);
                      }
                      break;
                    case 'addCategory':
                      handleNewPagesType(1);
                      break;
                    case 'addPage':
                      setIsInitPageType(true);
                      setCheckedPageData(false);
                      history.push(
                        `/app${match.params.appId}/${match.params.playType}`
                      );
                      break;
                    case 'collapse':
                      setIsCollapsePage(!isCollapsePage);
                      break;
                  }
                }
              }}
              data={{
                // 传递重命名状态到 data 中，让 MyNavPage 组件能够访问
                isRenamePageOrClass,
                renamePageOrClassItem
              }}
            />
            </>
          )}

          {/* 应用模板菜单 */}
          {isAppTemplate && (
            <AMISRenderer
              schema={{
                type: 'my-nav-page',
                stacked: true,
                className: 'app-template-nav',
                links: transformCombinedMenuToNavLinks(combinedMenuList),
                searchable: false,
                onSelect: (item: any) => {
                  handleTemplateMenuItemClick(item);
                },
                activeKey: getActiveMenuKey(),
                itemRender: (item: any) => renderTemplateNavItemContent(item),
                onCreatePage: (pageType: any) => {
                  console.log('模板菜单创建页面:', pageType);
                  handleCreatePageFromNav(pageType);
                },
                onAddCategory: () => handleNewPagesType(1)
              }}
              data={{}}
            />
          )}
        </>
      );
    };





    // 头部
    const renderHeader = () => {
      // 检查是否为全屏模式（应用设置、应用发布等）
      const isFullScreenMode = Boolean(match.params.form && ['appSetting', 'appPublish', 'pageManager', 'approval'].includes(match.params.form));

      // 中间导航配置
      const middleNavItems = match.params.playType === 'admin' ? [
        {
          key: 'pageManager',
          label: '页面管理',
          active: !['appSetting', 'appPublish', 'approval'].includes(match.params.form || ''),
          // active: match.params.form === 'pageManager' || !match.params.form,
          onClick: () => {
            setCheckedPageData(false);
            setIsInitPageType(true);
            history.push(`/app${match.params.appId}/${match.params.playType}/pageManager`);
          }
        },
        {
          key: 'appSetting',
          label: '应用设置',
          active: match.params.form === 'appSetting',
          onClick: () => {
            setCheckedPageData(false);
            setIsInitPageType(false);
            history.push(`/app${match.params.appId}/${match.params.playType}/appSetting/basicSetting`);
          }
        },
        {
          key: 'appPublish',
          label: '应用发布',
          active: match.params.form === 'appPublish',
          onClick: () => {
            setCheckedPageData(false);
            setIsInitPageType(false);
            history.push(`/app${match.params.appId}/${match.params.playType}/appPublish`);
          }
        },
        {
          key: 'approval',
          label: '审批',
          active: match.params.form === 'approval',
          onClick: () => {
            setCheckedPageData(false);
            setIsInitPageType(false);
            history.push(`/app${match.params.appId}/${match.params.playType}/approval`);
          }
        }
      ] : [];

      return (
        <CommonHeader
          store={store}
          history={history}
          type="app"
          className="applyPageHeader"
          buttonText={match.params.playType == 'admin' ? '应用搭建' : '工作台'}
          buttonIcon={match.params.playType == 'admin' ? dajian : gongzuo}
          onSwitchView={() => {
            history.push(
              match.params.playType == 'admin'
                ? '/appbuild/home'
                : '/platform/workbench'
            );
          }}
          applyData={applyData}
          openApplyNameInput={openApplyNameInput}
          editApplyName={editApplyName}
          match={match}
          onEditApplyName={openEditApplyName}
          onSaveApplyName={saveEditApplyName}
          showMiddleNav={match.params.playType === 'admin'}
          middleNavItems={middleNavItems}
          showPageTools={match.params.form === 'pageManager'}
          onSearchClick={() => {
            // TODO: 实现搜索功能
            toast.info('搜索功能开发中...');
          }}
          onAddCategoryClick={() => handleNewPagesType(1)}
          onAddPageClick={() => {
            setIsInitPageType(true);
            setCheckedPageData(false);
            history.push(`/app${match.params.appId}/${match.params.playType}`);
          }}
          onToggleCollapse={() => setIsCollapsePage(!isCollapsePage)}
          isCollapsed={isCollapsePage}
        />
      );
    };

    /* page 页面构建渲染 end */

    // 检查是否为全屏模式
    const isFullScreenMode = Boolean(match.params.form && ['appSetting', 'appPublish', 'pageManager', 'approval'].includes(match.params.form));

    // 全屏模式渲染
    if (isFullScreenMode) {
      return (
        <div className="full-screen-layout">
          {renderHeader()}
          <div className="full-screen-content">
            <Switch>
              {/* 页面管理 */}
              {match.params.form === 'pageManager' && (
                <PageManager
                  store={store}
                  history={history}
                  match={match}
                  location={location}
                  appMenuList={appMenuList}
                  isCollapsePage={isCollapsePage}
                  setIsCollapsePage={setIsCollapsePage}
                  handleNewPagesType={handleNewPagesType}
                  setIsInitPageType={setIsInitPageType}
                  setCheckedPageData={setCheckedPageData}
                  applyData={applyData}
                  openEditApplyName={openEditApplyName}
                  saveEditApplyName={saveEditApplyName}
                  openApplyNameInput={openApplyNameInput}
                  editApplyName={editApplyName}
                />
              )}

              {/* 应用设置 */}
              {match.params.form === 'appSetting' && !match.params.fullScreen && (
                <div className="full-screen-app-setting">
                  <AppSetting
                    store={store}
                    history={history}
                    match={match}
                    location={location}
                  />
                </div>
              )}

              {/* 应用发布 */}
              {match.params.form === 'appPublish' && (
                <div className="full-screen-app-publish">
                  <AppPublish
                    appId={match.params.appId}
                    store={store}
                    history={history}
                    pageData={checkedPageData}
                  />
                </div>
              )}

              {/* 审批 */}
              {match.params.form === 'approval' && (
                <div className="full-screen-approval">
                  <Approval
                    applyData={applyData}
                    store={store}
                    history={history}
                    match={match}
                    location={location}
                  />
                </div>
              )}

              {/* 全屏设置 */}
              {match.params.form === 'appSetting' &&
                match.params.appSetMenu === 'dataSet' &&
                !!match.params.fullScreen && (
                  <div className="full-screen-data-set">
                    <FullScreen
                      store={store}
                      history={history}
                      match={match}
                      location={location}
                    />
                  </div>
                )}
            </Switch>
          </div>
        </div>
      );
    }

    // 普通布局模式
    return (
      <>
        {/* 导航项设置按钮样式 */}
        <style>{`
          /* 导航项设置按钮样式 */
          .nav-item-settings-btn {
            opacity: 0;
            transition: opacity 0.2s ease;
            border-radius: 50% !important;
            width: 24px !important;
            height: 24px !important;
            min-width: 24px !important;
            min-height: 24px !important;
            padding: 0 !important;
            margin: 0 !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            background: rgba(0, 0, 0, 0.1) !important;
            border: none !important;
            position: relative !important;
            vertical-align: middle !important;
          }

          /* 确保设置按钮在导航项中垂直居中 */
          .cxd-Nav-Menu-item .nav-item-settings-btn {
            position: absolute !important;
            right: 4px !important;
            top: 50% !important;
            transform: translateY(-50%) !important;
          }

          /* 导航项悬浮时显示设置按钮 */
          .cxd-Nav-Menu-item:hover .nav-item-settings-btn {
            opacity: 1;
          }

          /* 确保导航项有足够的右边距容纳设置按钮 */
          .cxd-Nav-Menu-item {
            position: relative !important;
            padding-right: 10px !important;
          }

          /* 导航项链接样式调整 */
          .cxd-Nav-Menu-item-link {
            padding-right: 4px !important;
          }

          /* 设置按钮悬浮效果 */
          .nav-item-settings-btn:hover {
            background: rgba(0, 0, 0, 0.2) !important;
            transform: translateY(-50%) scale(1.1) !important;
          }

          /* 设置按钮图标样式 */
          .nav-item-settings-btn .fa {
            font-size: 12px;
            color: #666;
          }

          /* 下拉菜单样式 */
          .nav-item-settings-btn .cxd-DropDown-menu {
            min-width: 120px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            border: 1px solid #e0e0e0;
            padding: 8px 0;
            background: white;
          }

          /* 下拉菜单项样式 */
          .nav-item-settings-btn .cxd-DropDown-menu .nav-menu-item {
            justify-content: flex-start !important;
            padding: 12px 16px !important;
            border-radius: 0 !important;
            border: none !important;
            background: transparent !important;
            color: #333 !important;
            font-size: 14px !important;
            width: 100% !important;
            text-align: left !important;
            font-weight: normal !important;
            line-height: 1.4 !important;
          }

          .nav-item-settings-btn .cxd-DropDown-menu .nav-menu-item:hover {
            background: #f5f5f5 !important;
            color: #333 !important;
          }

          /* 删除按钮特殊样式 */
          .nav-item-settings-btn .cxd-DropDown-menu .nav-menu-item-danger {
            background: #ff4d4f !important;
            color: white !important;
            margin: 4px 8px 0 8px !important;
            border-radius: 4px !important;
            font-weight: 500 !important;
          }

          .nav-item-settings-btn .cxd-DropDown-menu .nav-menu-item-danger:hover {
            background: #ff7875 !important;
            color: white !important;
          }
        `}</style>
        <Layout
        aside={renderAside()}
        asideClassName={`asidePagesClass ${isCollapsePage ? 'collapsed' : ''}`}
        header={renderHeader()}
        headerClassName={'headerPagesClassAppPage'}
        headerFixed={true}
        folded={store.asideFolded}
        offScreen={store.offScreen}
      >
        {/* isInitPageType：显示创建页面类型；appMenuList.length：是否存在页面或者分类 ;match.params.form:是否存在from：路由地址*/}
        <Switch>
          {/* 创建页面类型的路由 */}
          {(isInitPageType || appMenuList.length == 0) &&
            !match.params.form &&
            match.params.playType == 'admin' && (
              <InitPageType
                onSaveSuccess={onSaveSuccess}
                pageTypeList={pageTypeList}
                appMenuList={appMenuList}
                history={history}
              />
            )}

          {/* 页面内容路由 */}
          {match.params.form && checkedPageData && checkedPageData.pageType && (
            <>
              {/* 普通表单 */}
              {checkedPageData.pageType == 1 && (
                <PageContent
                  pageData={checkedPageData}
                  history={history}
                  match={match}
                  store={store}
                  computedMatch={match}
                  updatePage={() =>
                    handleGetApplicationPageAndClassList(
                      undefined,
                      isAppTemplate
                    )
                  }
                />
              )}
              {/* 新建流程表单 */}
              {checkedPageData.pageType == 2 && (
                <PageProcessContent
                  pageData={checkedPageData}
                  history={history}
                  match={match}
                  store={store}
                  computedMatch={match}
                  updatePage={() =>
                    handleGetApplicationPageAndClassList(
                      undefined,
                      isAppTemplate
                    )
                  }
                />
              )}
              {/* Excel导入 */}
              {/* {checkedPageData.pageType == 3 && (
                    <PageReportContent
                      pageData={checkedPageData}
                      history={history}
                      updatePage={() => handleGetApplicationPageAndClassList()}
                    />
                  )} */}
              {/* 外部链接 */}
              {checkedPageData.pageType == 4 && (
                <PagelinkContent
                  pageData={checkedPageData}
                  history={history}
                  match={match}
                  computedMatch={match}
                  updatePage={() =>
                    handleGetApplicationPageAndClassList(
                      undefined,
                      isAppTemplate
                    )
                  }
                />
              )}
              {/* 自定义 */}
              {checkedPageData.pageType == 5 && (
                <PageCustompage
                  pageData={checkedPageData}
                  history={history}
                  match={match}
                  computedMatch={match}
                  updatePage={() =>
                    handleGetApplicationPageAndClassList(
                      undefined,
                      isAppTemplate
                    )
                  }
                />
              )}
              {/* wiki文档 */}
              {/* {match.params.form &&
                  checkedPageData &&
                  checkedPageData.pageType == 6 && (
                    <PageWikiContent
                      pageData={checkedPageData}
                      history={history}
                      updatePage={() => handleGetApplicationPageAndClassList()}
                    />
                  )} */}
              {/* amis报表 */}
              {checkedPageData.pageType == 7 && (
                <PageDashboardContent
                  pageData={checkedPageData}
                  history={history}
                  match={match}
                  computedMatch={match}
                  updatePage={() =>
                    handleGetApplicationPageAndClassList(
                      undefined,
                      isAppTemplate
                    )
                  }
                />
              )}
              {/* 网盘 */}
              {checkedPageData.pageType == 8 && (
                <PageYunWangPanContent
                  pageData={checkedPageData}
                  history={history}
                  store={store}
                  match={match}
                  computedMatch={match}
                  updatePage={() =>
                    handleGetApplicationPageAndClassList(
                      undefined,
                      isAppTemplate
                    )
                  }
                />
              )}
              {/* 笔记 */}
              {checkedPageData.pageType == 9 && (
                <PageNotesContent
                  pageData={checkedPageData}
                  history={history}
                  store={store}
                  match={match}
                  computedMatch={match}
                  updatePage={() =>
                    handleGetApplicationPageAndClassList(
                      undefined,
                      isAppTemplate
                    )
                  }
                />
              )}
              {/* 多维表格 */}
              {/* {match.params.form &&
                  checkedPageData &&
                  checkedPageData.pageType == 10 && (
                    <PageMultiDimensionalTableContent
                      pageData={checkedPageData}
                      history={history}
                      updatePage={() => handleGetApplicationPageAndClassList()}
                    />
                  )} */}
              {/* 白板 */}
              {checkedPageData.pageType == 10 && (
                <PageWhiteboardContent
                  pageData={checkedPageData}
                  history={history}
                  store={store}
                  match={match}
                  computedMatch={match}
                  updatePage={() =>
                    handleGetApplicationPageAndClassList(
                      undefined,
                      isAppTemplate
                    )
                  }
                />
              )}
              {/* CRUD */}
              {/* {match.params.form &&
                  checkedPageData &&
                  checkedPageData.pageType == 12 && (
                    <PageCRUDpage
                      pageData={checkedPageData}
                      history={history}
                      updatePage={() => handleGetApplicationPageAndClassList()}
                    />
                  )} */}
              {/* 数据源导入 */}
              {checkedPageData.pageType == 13 && (
                <PageDataSetImport
                  pageData={checkedPageData}
                  history={history}
                  match={match}
                  computedMatch={match}
                  updatePage={() =>
                    handleGetApplicationPageAndClassList(
                      undefined,
                      isAppTemplate
                    )
                  }
                />
              )}
              {/* 芋道网盘 */}
              {checkedPageData.pageType == 14 && (
                <PageYudaoPanContent
                  pageData={checkedPageData}
                  history={history}
                  store={store}
                  match={match}
                  computedMatch={match}
                  updatePage={() =>
                    handleGetApplicationPageAndClassList(
                      undefined,
                      isAppTemplate
                    )
                  }
                />
              )}

              {/* 数据管理页面 */}
              {checkedPageData.pageType == 15 && (
                <PageDataManage
                  pageData={checkedPageData}
                  store={store}
                  history={history}
                  match={match}
                  computedMatch={match}
                  updatePage={() =>
                    handleGetApplicationPageAndClassList(
                      undefined,
                      isAppTemplate
                    )
                  }
                />
              )}

              {/* 门户 */}
              {checkedPageData.pageType == 17 && (
                <PagePortletContent
                  pageData={checkedPageData}
                  history={history}
                  match={match}
                  computedMatch={match}
                  updatePage={() =>
                    handleGetApplicationPageAndClassList(
                      undefined,
                      isAppTemplate
                    )
                  }
                />
              )}
            </>
          )}

          {/* 系统功能路由 - 不依赖于菜单项 */}
          {match.params.form && (
            <>
              {/* 待处理 */}
              {match.params.form == 'pending' && (
                <div className="applyReportMian">
                  <AMISRenderer
                    schema={createReportObj(
                      `${fixedPlayMenu.pending.path}?applyID=${applyData.id}&userID=${store.userInfo?.id}&tenantId=${store.tenant_id}&authorization=${store.access_token}`
                    )}
                    embedMode={true}
                  />
                </div>
              )}

              {/* 已处理 */}
              {match.params.form == 'transactors' && (
                <div className="applyReportMian">
                  <AMISRenderer
                    schema={createReportObj(
                      `${fixedPlayMenu.transactors.path}?applyID=${applyData.id}&userID=${store.userInfo?.id}&tenantId=${store.tenant_id}&authorization=${store.access_token}`
                    )}
                    embedMode={true}
                  />
                </div>
              )}

              {/* 我创建的 */}
              {match.params.form == 'create' && (
                <div className="applyReportMian">
                  <AMISRenderer
                    schema={createReportObj(
                      `${fixedPlayMenu.create.path}?applyID=${applyData.id}&userID=${store.userInfo?.id}&tenantId=${store.tenant_id}&authorization=${store.access_token}`
                    )}
                    embedMode={true}
                  />
                </div>
              )}

              {/* 抄送我的 */}
              {match.params.form == 'ccusers' && (
                <div className="applyReportMian">
                  <AMISRenderer
                    schema={createReportObj(
                      `${fixedPlayMenu.ccusers.path}?applyID=${applyData.id}&userID=${store.userInfo?.id}&tenantId=${store.tenant_id}&authorization=${store.access_token}`
                    )}
                    embedMode={true}
                  />
                </div>
              )}

              {/* 审批 */}
              {match.params.form == 'approval' && (
                <Approval
                  applyData={applyData}
                  store={store}
                  history={history}
                  match={match}
                  location={location}
                />
              )}

              {/* 应用发布 */}
              {match.params.form === 'appPublish' && (
                <AppPublish
                  appId={match.params.appId}
                  store={store}
                  history={history}
                  pageData={checkedPageData}
                />
              )}

              {/* 应用设置 */}
              {match.params.form == 'appSetting' &&
                !match.params.fullScreen && (
                  <div className="applyReportMian">
                    <AppSetting
                      store={store}
                      history={history}
                      match={match}
                      location={location}
                    />
                  </div>
                )}

              {/* 全屏设置 */}
              {match.params.form == 'appSetting' &&
                match.params.appSetMenu == 'dataSet' &&
                !!match.params.fullScreen && (
                  <div className="applyReportMian">
                    <FullScreen
                      store={store}
                      history={history}
                      match={match}
                      location={location}
                    />
                  </div>
                )}

              {/* 数据源管理 */}
              {match.params.form == 'dataSource' && (
                <div className="applyReportMian">
                  <DataSource
                    history={history}
                    store={store}
                    applyData={applyData}
                  />
                </div>
              )}

              {/* 数据集管理 */}
              {match.params.form == 'dataSet' && (
                <div className="applyReportMian">
                  <DataSet
                    history={history}
                    store={store}
                    applyData={applyData}
                  />
                </div>
              )}

              {/* 控件字典 */}
              {match.params.form == 'controlDictionary' && (
                <div className="applyReportMian">
                  <ControlDictionary
                    history={history}
                    store={store}
                    applyData={applyData}
                  />
                </div>
              )}

              {/* 菜单管理 */}
              {match.params.form == 'menuManager' && (
                <div className="applyReportMian">
                  <MenuManager
                    history={history}
                    store={store}
                    applyData={applyData}
                    // match={match}
                    onMenuRefresh={() => handleGetApplicationPageAndClassList()}
                    onCreatePage={() => {
                      setIsInitPageType(true);
                      setCheckedPageData(false);
                      history.push(
                        `/app${match.params.appId}/${match.params.playType}`
                      );
                    }}
                  />
                </div>
              )}
            </>
          )}
        </Switch>
        {rightPageMenu && match.params.playType == 'admin' && (
          <div
            id="customContextMenu"
            className="customContextMenu"
            style={{
              left: rightPageMenuPosition.x + 'px',
              top: rightPageMenuPosition.y + 'px'
            }}
          >
            {rightTypeMenu == 1 && (
              <>
                <div
                  className="customContextMenu-item"
                  onClick={() => handleReName()}
                >
                  {/* <i className="fa fa-pen customContextMenu-item-icon"></i> */}
                  <div className="customContextMenu-item-name">重命名</div>
                </div>
                <div
                  className="customContextMenu-item"
                  onClick={() => delClassOrPage(rightMenuItem)}
                >
                  {/* <i className="fa fa-trash customContextMenu-item-icon"></i> */}
                  <div className="customContextMenu-item-name">删除页面</div>
                </div>
              </>
            )}
            {rightTypeMenu == 2 && (
              <>
                <div
                  className="customContextMenu-item"
                  onClick={() => handleReName()}
                >
                  {/* <i className="fa fa-pen customContextMenu-item-icon"></i> */}
                  <div className="customContextMenu-item-name">重命名</div>
                </div>
                <div
                  className="customContextMenu-item"
                  onClick={() => cancelClass(rightMenuItem)}
                >
                  {/* <i className="fa fa-trash customContextMenu-item-icon"></i> */}
                  <div className="customContextMenu-item-name">取消分类</div>
                </div>
                <div
                  className="customContextMenu-item"
                  onClick={() => delClassOrPage(rightMenuItem)}
                >
                  {/* <i className="fa fa-trash customContextMenu-item-icon"></i> */}
                  <div className="customContextMenu-item-name">删除分类</div>
                </div>
              </>
            )}
          </div>
        )}
        {/* 退出确认弹窗 */}
        <AMISRenderer
          show={showLogoutDialog}
          onClose={() => setShowLogoutDialog(false)}
          onConfirm={() => {
            setShowLogoutDialog(false);
            LogOut();
          }}
          schema={LogoutConfirmDialog(showLogoutDialog)}
        />

        {/* 图标编辑对话框 */}
        {showEditIconDialog && (
          <AMISRenderer
            show={showEditIconDialog}
            onClose={() => {
              setShowEditIconDialog(false);
              setEditIconItem(null);
            }}
            onConfirm={handleIconSave}
            schema={EditEntryIcon({
              logo: (() => {
                // 解析当前页面图标，获取正确的图标数据
                const parsedIcon = parseAppIcon(editIconItem?.icon || '');
                return {
                  icon: parsedIcon.icon,
                  iconColor: parsedIcon.iconColor,
                  iconBg: parsedIcon.iconBg
                };
              })()
            })}
          />
        )}
      </Layout>
      </>
    );
  })
);
