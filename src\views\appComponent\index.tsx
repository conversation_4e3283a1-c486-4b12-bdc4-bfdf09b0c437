import React, {FC} from 'react';
import './index.scss';
import {<PERSON><PERSON>, toast} from 'amis';
import <PERSON>IS<PERSON>enderer from '@/component/AMISRenderer';
import type_folder from '@/image/page_icons/type_folder.png';
import {EditIcon} from '@/utils/schemaDataSet/EditIcon';
import {
  getSystemComponentClassificationPage,
  getOrgComponentClassificationPage
} from '@/utils/api/api';
import {visibleOn} from '@/editor/EChartsEditor/Common';
import {
  formItems,
  containerItems,
  dataItems,
  functionItems,
  classOption,
  showItems
} from '@/utils/amisEditorItems/formitems';
// 预留接口函数
const getSystemComponentPage = (params: any) => {
  // TODO: 实现获取系统组件列表接口
  return Promise.resolve({
    code: 0,
    data: {
      list: [],
      total: 0
    }
  });
};

// 获取组件配置数据
const getSystemComponentConfigs = () => {
  // TODO: 实现获取系统组件配置接口
  // 这里应该从后端API获取组件配置数据
  return Promise.resolve({
    code: 0,
    data: [
      // 示例配置数据
      {
        name: '页面',
        classificationName: '布局容器',
        componentType: 'page',
        status: 1,
        sort: 1
      },
      {
        name: '包裹',
        classificationName: '布局容器',
        componentType: 'wrapper',
        status: 1,
        sort: 2
      }
    ]
  });
};

const createSystemComponent = (params: any) => {
  // TODO: 实现创建系统组件接口
  return Promise.resolve({
    code: 0,
    data: {}
  });
};

const updateSystemComponent = (params: any) => {
  // TODO: 实现更新系统组件接口
  return Promise.resolve({
    code: 0,
    data: {}
  });
};

const deleteSystemComponent = (params: any) => {
  // TODO: 实现删除系统组件接口
  return Promise.resolve({
    code: 0,
    data: {}
  });
};

const updateSystemComponentClassification = (params: any) => {
  // TODO: 实现更新系统组件分类接口
  return Promise.resolve({
    code: 0,
    data: {}
  });
};

const deleteSystemComponentClassification = (params: any) => {
  // TODO: 实现删除系统组件分类接口
  return Promise.resolve({
    code: 0,
    data: {}
  });
};

const SystemComponent: FC<any> = (props: any) => {
  // 获取URL参数中的应用ID
  const applicationId = props.match?.params?.appId || props.applyData?.id;

  const [categories, setCategories] = React.useState<any[]>([]);
  const [selectedCategory, setSelectedCategory] = React.useState<any>(null);
  const [showCreateCategoryModal, setShowCreateCategoryModal] =
    React.useState(false);
  const [showCreateComponentModal, setShowCreateComponentModal] =
    React.useState(false);
  const [showDeleteCategoryModal, setShowDeleteCategoryModal] =
    React.useState(false);
  const [categoryToDelete, setCategoryToDelete] = React.useState<any>(null);
  const [newCategoryName, setNewCategoryName] = React.useState('');
  const [categoryEditMode, setCategoryEditMode] = React.useState('create');
  const [categoryToEdit, setCategoryToEdit] = React.useState<any>(null);
  const [rightMenuVisible, setRightMenuVisible] = React.useState(false);
  const [rightMenuPosition, setRightMenuPosition] = React.useState({
    x: 0,
    y: 0
  });
  const [rightMenuItem, setRightMenuItem] = React.useState<any>(null);
  const [searchKeyword, setSearchKeyword] = React.useState('');
  const [searchStatus, setSearchStatus] = React.useState('');
  const [amisKey, setAmisKey] = React.useState(0);
  const [showEditIconDialog, setShowEditIconDialog] = React.useState(false);
  const [iconValue, setIconValue] = React.useState<string>('');

  // 获取分类列表
  const fetchCategoryList = () => {
    getOrgComponentClassificationPage({
      pageNo: 1,
      pageSize: 100,
      type: 3,
      applicationId: applicationId
    })
      .then(result => {
        if (result.code === 0) {
          setCategories([
            {
              id: 'all',
              name: '全部分类'
            },
            ...result.data.list
          ]);
        }
      })
      .catch(error => {
        console.error('获取分类列表失败：', error);
        setCategories([
          {
            id: 'all',
            name: '全部分类'
          }
        ]);
      });

    setSelectedCategory({
      id: 'all',
      name: '全部分类'
    });
  };

  // 创建新分类
  const handleCreateCategory = () => {
    setShowCreateCategoryModal(true);
    setNewCategoryName('');
    setCategoryEditMode('create');
    setCategoryToEdit(null);
  };

  // 重命名分类
  const handleRenameCategory = (category: any) => {
    if (!category || category.id === 'all') return;
    setShowCreateCategoryModal(true);
    setCategoryEditMode('rename');
    setCategoryToEdit(category);
  };

  // 删除分类
  const handleDeleteCategory = (category: any) => {
    if (!category || category.id === 'all') return;
    setCategoryToDelete(category);
    setShowDeleteCategoryModal(true);
    setRightMenuVisible(false);
  };

  // 确认删除分类
  const confirmDeleteCategory = () => {
    if (!categoryToDelete) return;

    deleteSystemComponentClassification({
      id: categoryToDelete.id
    })
      .then(result => {
        if (result.code === 0) {
          toast.success('分类删除成功');
          const updatedCategories = categories.filter(
            cat => cat.id !== categoryToDelete.id
          );
          setCategories(updatedCategories);
          if (selectedCategory?.id === categoryToDelete.id) {
            setSelectedCategory({
              id: 'all',
              name: '全部分类',
              type: 1
            });
          }
        } else {
          // toast.error(result.msg || '删除失败');
        }
        setShowDeleteCategoryModal(false);
      })
      .catch(error => {
        console.error('删除分类失败', error);
        toast.error('删除分类失败，请稍后重试');
        setShowDeleteCategoryModal(false);
      });
  };

  let addDialogSchema = {
    type: 'dialog',
    title: '新建组织组件',
    body: [
      {
        type: 'form',
        api: {
          method: 'post',
          url: '/admin-api/system/control-dict/create',
          data: {
            "&": "$$",
            type: 3,
            applicationId: applicationId,
          }
        },
        mode: 'horizontal',
        horizontal: {
          left: 2,
          right: 10,
          offset: 2
        },
        body: [
          {
            type: 'input-text',
            label: '控件名称',
            name: 'controlType',
            required: true,
            placeholder: '请输入控件名称'
          },
          {
            type: 'select',
            selectMode: 'associated',
            label: '控件类型',
            searchable: true,
            name: 'amisControlType',
            required: true,
            leftOptions: classOption,
            options: [
              ...formItems,
              ...containerItems,
              ...dataItems,
              ...functionItems,
              ...showItems
            ]
          },
          // {
          //   type: 'editor',
          //   label: 'JSON',
          //   name: 'json',
          //   language: 'json',
          //   required: true,
          //   placeholder: '请输入组件的JSON配置'
          // },
          {
            type: 'select',
            name: 'classificationId',
            label: '分类',
            required: true,
            options: categories
              .filter(category => category.id !== 'all')
              .map(category => ({
                label: category.name,
                value: category.id
              }))
          },
          {
            label: '启用',
            type: 'switch',
            name: 'status',
            falseValue: '0',
            trueValue: '1',
            onText: '已启用',
            offText: '未启用',
            value: '1'
          }
        ],
        actions: [
          {
            type: 'button',
            label: '取消',
            onEvent: {
              click: {
                actions: [
                  {
                    actionType: 'cancel'
                  }
                ]
              }
            },
            level: 'default'
          },
          {
            type: 'button',
            label: '提交',
            onEvent: {
              click: {
                actions: [
                  {
                    actionType: 'submit'
                  }
                ]
              }
            },
            level: 'primary'
          }
        ],
        feat: 'Add',
        dsType: 'api',
        title: '表单',
        resetAfterSubmit: true
      }
    ],
    actionType: 'drawer',
    actions: [
      {
        type: 'button',
        actionType: 'cancel',
        label: '取消'
      },
      {
        type: 'button',
        actionType: 'confirm',
        label: '确定',
        primary: true
      }
    ],
    showCloseButton: true,
    closeOnOutside: false,
    closeOnEsc: false,
    showErrorMsg: true,
    showLoading: true,
    draggable: false,
    size: 'lg',
    resizable: false,
    editorSetting: {
      displayName: '新建组织组件'
    }
  };

  // 处理右键菜单
  const handleContextMenu = (e: React.MouseEvent, category: any) => {
    e.preventDefault();
    const rect = document.documentElement.getBoundingClientRect();
    const windowHeight = rect.bottom - rect.top;

    const isAboveHalf = e.clientY < windowHeight / 2;
    setRightMenuPosition({
      x: e.clientX,
      y: isAboveHalf ? e.clientY : e.clientY - 100
    });

    setRightMenuItem(category);
    setRightMenuVisible(true);
  };

  // 点击其他地方关闭右键菜单
  React.useEffect(() => {
    const handleClickOutside = () => {
      setRightMenuVisible(false);
    };

    if (rightMenuVisible) {
      document.addEventListener('click', handleClickOutside);
    }

    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, [rightMenuVisible]);

  React.useEffect(() => {
    fetchCategoryList();
  }, []);

  return (
    <div className="pageBox">
      {/* 隐藏元素存储当前图标值 */}
      <div
        id="current-icon-value"
        data-value={iconValue}
        style={{display: 'none'}}
      ></div>

      <div
        className="pageTop"
        style={{display: 'flex', flexDirection: 'column', left: 0}}
      >
        <div className="pageTop-title">应用组件管理</div>
        <div>
          可以自定义应用级的组件，并在Editor编辑器中进行调用，当前应用可以调用，跨应用不可调用。
        </div>
      </div>

      {/* 顶部查询区域 */}
      <div className="component-header">
        <div className="search-area">
          <AMISRenderer
            schema={{
              type: 'form',
              wrapWithPanel: false,
              mode: 'inline',
              target: 'crudId',
              submitOnChange: false,
              body: [
                {
                  type: 'input-text',
                  name: 'name',
                  label: '组件名称',
                  placeholder: '请输入组件名称',
                  clearable: true,
                  value: searchKeyword
                },
                {
                  type: 'select',
                  name: 'status',
                  label: '状态',
                  placeholder: '请选择',
                  clearable: true,
                  value: searchStatus,
                  options: [
                    {label: '启用', value: '1'},
                    {label: '禁用', value: '0'}
                  ]
                },
                {
                  type: 'submit',
                  label: '搜索',
                  level: 'primary',
                  onClick: (e: any, context: any) => {
                    setSearchKeyword(context.data.name || '');
                    setSearchStatus(context.data.status || '');
                    setAmisKey(Date.now());
                  }
                },
                {
                  type: 'reset',
                  label: '重置',
                  onClick: () => {
                    setSearchKeyword('');
                    setSearchStatus('');
                    setAmisKey(Date.now());
                  }
                }
              ]
            }}
          />
        </div>
      </div>

      {/* 顶部操作区域 */}
      {/* <div className="component-header" style={{display: 'flex', justifyContent: 'space-between'}}>
        <div className="action-area">
          <Button level="primary" onClick={() => setShowCreateComponentModal(true)}>
            新建组件
          </Button>
        </div>
      </div> */}

      <div className="component-container">
        {/* 左侧分类栏 */}
        <div className="category-sidebar">
          <div className="category-header">
            <span>分类</span>
            <Button
              level="link"
              size="sm"
              onClick={handleCreateCategory}
              className="add-category-btn"
            >
              <i className="fa fa-plus"></i>
            </Button>
          </div>
          <div className="category-list">
            {categories.map(category => (
              <div
                key={category.id}
                className={`category-item ${
                  selectedCategory?.id === category.id ? 'active' : ''
                }`}
                onClick={() => {
                  setSelectedCategory(category);
                  setAmisKey(Date.now());
                }}
                onContextMenu={e => handleContextMenu(e, category)}
              >
                <div className="category-item-content">
                  <img src={type_folder} className="category-icon" />
                  <span className="category-name">{category.name}</span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 右侧内容区 */}
        <div className="content-area">
          <AMISRenderer
            key={amisKey}
            schema={{
              type: 'page',
              body: [
                {
                  type: 'crud',
                  syncLocation: false,
                  id: 'crudId',
                  showIndex: true,
                  draggable: true,
                  api: {
                    method: 'get',
                    url: '/admin-api/system/control-dict/page',
                    data: {
                      pageNo: '${page}',
                      pageSize: '${perPage}',
                      classificationId:
                        selectedCategory && selectedCategory.id !== 'all'
                          ? selectedCategory.id
                          : '${classificationId}',
                          controlType: searchKeyword,
                      status: searchStatus,
                      type: 3,
                      applicationId: applicationId
                    }
                  },
                  columns: [
                    {
                      type: 'tpl',
                      tpl: '${classificationName}',
                      label: '分类'
                    },
                    {
                      type: 'tpl',
                      tpl: '${name}',
                      label: '应用组件名称'
                    },
                    {
                      type: 'tpl',
                      tpl: '${componentType}',
                      label: '组件类型'
                    },
                    {
                      label: '描述',
                      name: 'description',
                      type: 'tpl'
                    },
                    // {
                    //   type: 'switch',
                    //   name: 'status',
                    //   label: '启用状态',
                    //   static: true,
                    //   quickSaveItemApi: {
                    //     method: 'put',
                    //     url: '/admin-api/system/component-manage/update',
                    //     data: {
                    //       "status": "${status ? 1 : 0}",
                    //       "id": "${id}"
                    //     }
                    //   }
                    // },
                    {
                      type: 'status',
                      label: '启用状态',
                      name: 'status',
                      source: {
                        '1': {
                          label: '已启用',
                          // "icon": "fa fa-times-circle",
                          color: '#28a745'
                        },
                        '0': {
                          label: '已停用',
                          // "icon": "fa fa-times-circle",
                          color: '#f00'
                        }
                      },
                      value: 'normal'
                    },
                    {
                      type: 'user-select',
                      name: 'updater',
                      label: '编辑人',
                      static: true
                    },
                    {
                      type: 'input-datetime',
                      name: 'updateTime',
                      label: '编辑时间',
                      valueFormat: 'x',
                      static: true
                    },
                    {
                      type: 'user-select',
                      name: 'creator',
                      label: '创建人',
                      static: true
                    },
                    {
                      type: 'input-datetime',
                      name: 'createTime',
                      label: '创建时间',
                      valueFormat: 'x',
                      static: true
                    },
                    {
                      type: 'operation',
                      label: '操作',
                      buttons: [
                        {
                          label: 'amis编辑',
                          type: 'button',
                          level: 'link',
                          onClick: (event: any, actionProps: any) => {
                            // console.log(event, actionProps.data.id)
                            // console.log('clocked')
                            // console.log()
                            props.history.push(`/controlEditor/${actionProps.data.id}` + '?type=edit');
                          }
                            // props.history.push('/dsad/admin/1');
                            // props.history.push(`/dsad/admin/${actionProps.data.id}`);
                        },
                        {
                          label: 'JSON编辑',
                          type: 'button',
                          level: 'link',
                          id: 'u:json_edit',
                          actionType: 'dialog',
                          dialog: {
                            type: 'dialog',
                            title: 'JSON编辑',
            
                            body: [
                              {
                                type: 'form',
                                mode: 'horizontal',
                                horizontal: {
                                  left: 2,
                                  right: 10,
                                  offset: 2
                                },
                                api: {
                                  method: 'put',
                                  url: `/admin-api/system/control-dict/update`,
                                  data: {
                                    id: '${id}',
                                    controlType: '${controlType}',
                                    amisControlType: '${amisControlType}',
                                    json: '${json}',
                                    status: '${status}'
                                  }
                                },
                                body: [
                                  {
                                    type: "editor",
                                    name: "json",
                                    label: "编辑器",
                                    language: "json",}]
                                // placeholder: "function() {\n  console.log('hello world')\n}"
                              }
                            ]
                          }
                        },
                        {
                          label: '编辑',
                          type: 'button',
                          actionType: 'dialog',
                          level: 'link',
                          editorSetting: {
                            behavior: 'update'
                          },
                          id: 'u:4d423864d6c0',
                          dialog: {
                            type: 'dialog',
                            title: '编辑',
                            body: [
                              {
                                type: 'form',
                                api: {
                                  method: 'put',
                                  url: '/admin-api/system/control-dict/update',
                                },
                                mode: 'horizontal',
                                horizontal: {
                                  left: 2,
                                  right: 10,
                                  offset: 2
                                },
                                body: [{
                                  type: 'input-text',
                                  label: 'ID',
                                  name: 'id',
                                  value: '${id}',
                                  hidden: true
                                },
                                {
                                  type: 'input-text',
                                  label: '控件名称',
                                  name: 'controlType',
                                  value: '${controlType}',
                                },
                                {type: 'select',selectMode: "associated", label: '控件类型', searchable: true, name: 'amisControlType', id: 'u:amisControlType', leftOptions: classOption, options: [...formItems, ...containerItems, ...dataItems, ...functionItems, ...showItems]},
                                {
                                  type: 'editor',
                                  label: 'JSON',
                                  name: 'json',
                                  language: "json",
                                  value: '${json}',
                                },
                                {
                                  label: '启用',
                                  type: 'switch',
                                  name: 'status',
                                  id: 'u:status',
                                  falseValue: '0',
                                  trueValue: '1',
                                  onText: '已启用',
                                  offText: '未启用'
                                },
                              ],
                                actions: [
                                  {
                                    type: 'button',
                                    label: '取消',
                                    onEvent: {
                                      click: {
                                        actions: [
                                          {
                                            actionType: 'cancel',
                                            componentId: 'u:1fb5ccb04730'
                                          }
                                        ]
                                      }
                                    },
                                    level: 'default'
                                  },
                                  {
                                    type: 'button',
                                    label: '提交',
                                    onEvent: {
                                      click: {
                                        actions: [
                                          {
                                            actionType: 'submit',
                                            componentId: 'u:1fb5ccb04730'
                                          }
                                        ]
                                      }
                                    },
                                    level: 'primary'
                                  }
                                ],
                                feat: 'Edit',
                                dsType: 'api',
                                // labelAlign: 'top',
                                title: '表单',
                                resetAfterSubmit: true
                              }
                            ],
                            actionType: 'drawer',
                            actions: [
                              {
                                type: 'button',
                                actionType: 'cancel',
                                label: '取消',
                                id: 'u:2e86c554e48e'
                              },
                              {
                                type: 'button',
                                actionType: 'confirm',
                                label: '确定',
                                primary: true,
                                id: 'u:392213d59e8d'
                              }
                            ],
                            showCloseButton: true,
                            closeOnOutside: false,
                            closeOnEsc: false,
                            showErrorMsg: true,
                            showLoading: true,
                            draggable: false,
                            size: 'lg',
                            resizable: false,
                            editorSetting: {
                              displayName: '编辑'
                            }
                          }
                        },
                        {
                          label: '查看',
                          type: 'button',
                          actionType: 'drawer',
                          level: 'link',
                          editorSetting: {
                            behavior: 'view'
                          },
                          drawer: {
                            type: 'drawer',
                            title: '查看详情',
                            body: [
                              {
                                type: 'form',
                                body: [
                                  {
                                    label: 'ID',
                                    type: 'text',
                                    name: 'id',
                                  },
                                  {
                                    label: '分类',
                                    type: 'select',
                                    name: 'classification',
                                    static: true
                                  },
                                  {
                                    label: '控件名称',
                                    type: 'text',
                                    name: 'controlType',
                                  },
                                  {
                                    label: '控件类型',
                                    type: 'select',
                                    name: 'amisControlType',
                                    static: true
                                  },
                                  {
                                    label: 'JSON',
                                    type: 'text',
                                    name: 'json',
                                  },
                                  {
                                    label: '启用',
                                    type: 'switch',
                                    name: 'status',
                                    falseValue: '0',
                                    trueValue: '1',
                                    onText: '已启用',
                                    offText: '未启用'
                                  },
                                  {
                                    label: '创建时间',
                                    type: 'input-datetime',
                                    name: 'createTime',
                                    valueFormat: 'x',
                                    displayFormat: 'YYYY-MM-DD HH:mm:ss',
                                    static: true
                                  },
                                ],
                                id: 'u:2af2f2b5c2fd',
                                feat: 'View',
                                dsType: 'api',
                                labelAlign: 'top',
                                title: '表单',
                                mode: 'flex',
                                static: true,
                                actions: [
                                  {
                                    type: 'button',
                                    label: '取消',
                                    onEvent: {
                                      click: {
                                        actions: [
                                          {
                                            actionType: 'cancel',
                                            componentId: 'u:e9f0e2b1f53a'
                                          }
                                        ]
                                      }
                                    },
                                    level: 'default'
                                  },
                                  {
                                    type: 'button',
                                    label: '提交',
                                    onEvent: {
                                      click: {
                                        actions: [
                                          {
                                            actionType: 'submit',
                                            componentId: 'u:e9f0e2b1f53a'
                                          }
                                        ]
                                      }
                                    },
                                    level: 'primary'
                                  }
                                ]
                              }
                            ],
                            actionType: 'drawer',
                            actions: [
                              {
                                type: 'button',
                                actionType: 'cancel',
                                label: '取消'
                              },
                              {
                                type: 'button',
                                actionType: 'confirm',
                                label: '确定',
                                primary: true
                              }
                            ],
                            showCloseButton: true,
                            closeOnOutside: false,
                            closeOnEsc: false,
                            showErrorMsg: true,
                            showLoading: true,
                            draggable: false,
                            size: 'lg',
                            resizable: false,
                            editorSetting: {
                              displayName: '查看详情'
                            }
                          }
                        },
                        {
                          type: 'button',
                          label: '删除',
                          actionType: 'dialog',
                          level: 'link',
                          className: 'text-danger',
                          dialog: {
                            type: 'dialog',
                            title: '',
                            className: 'py-2',
                            actions: [
                              {
                                type: 'action',
                                actionType: 'cancel',
                                label: '取消'
                              },
                              {
                                type: 'action',
                                actionType: 'submit',
                                label: '删除',
                                level: 'danger'
                              }
                            ],
                            body: [
                              {
                                type: 'form',
                                wrapWithPanel: false,
                                api:
                                  `delete:/admin-api/system/control-dict/delete?id=` +
                                  '${id}',
                                body: [
                                  {
                                    type: 'tpl',
                                    className: 'py-2',
                                    tpl: '确认删除选中项？'
                                  }
                                ],
                                feat: 'Insert',
                                dsType: 'api',
                                labelAlign: 'left'
                              }
                            ],
                            actionType: 'dialog',
                            showCloseButton: true,
                            closeOnOutside: false,
                            closeOnEsc: false,
                            showErrorMsg: true,
                            showLoading: true,
                            draggable: false,
                            editorSetting: {
                              displayName: '删除'
                            }
                          }
                        }
                      ],
                      id: 'u:8b6efbd2f849'
                    }
                  ],
                  headerToolbar: ['reload',
                    {
                      label: '新增应用组件',
                      type: 'button',
                      actionType: 'dialog',
                      dialog: addDialogSchema
                    }
                  ],
                  footerToolbar: ['statistics', 'switch-per-page', 'pagination']
                }
              ]
            }}
            embedMode={true}
          />
        </div>
      </div>
      {/* 分类编辑模态框 */}
      {showCreateCategoryModal && (
        <AMISRenderer
          show={showCreateCategoryModal}
          onClose={() => setShowCreateCategoryModal(false)}
          schema={{
            type: 'dialog',
            title: categoryEditMode === 'create' ? '新建分类' : '编辑分类',
            body: {
              type: 'form',
              api: {
                method: categoryEditMode === 'create' ? 'post' : 'put',
                url:
                  categoryEditMode === 'create'
                    ? '/admin-api/system/control-dict-classification/create'
                    : '/admin-api/system/control-dict-classification/update',
                data: {
                  name: '${name}',
                  id:
                    categoryEditMode === 'rename' && categoryToEdit
                      ? categoryToEdit.id
                      : null,
                      type: 3,
                      applicationId: applicationId
                },
                adaptor: function (
                  payload: any,
                  response: any,
                  api: any,
                  context: any
                ) {
                  setShowCreateCategoryModal(false);
                  fetchCategoryList();
                  return payload;
                }
              },
              body: [
                {
                  type: 'input-text',
                  name: 'name',
                  label: '分类名称',
                  placeholder: '请输入分类名称',
                  value:
                    categoryEditMode === 'rename' && categoryToEdit
                      ? categoryToEdit.name
                      : '',
                  required: true
                }
              ]
            },
            actions: [
              {
                type: 'button',
                label: '取消',
                actionType: 'cancel'
              },
              {
                type: 'button',
                label: '确定',
                level: 'primary',
                actionType: 'submit'
              }
            ]
          }}
        />
      )}

      {/* 删除分类确认对话框 */}
      {showDeleteCategoryModal && (
        <AMISRenderer
          show={showDeleteCategoryModal}
          schema={{
            type: 'dialog',
            title: '删除确认',
            body: {
              type: 'form',
              body: [
                {
                  type: 'tpl',
                  tpl: `确定要删除"${categoryToDelete?.name}"分类吗？`,
                  className: 'py-2 text-center'
                }
              ]
            },
            actions: [
              {
                type: 'button',
                label: '取消',
                actionType: 'cancel',
                onClick: () => setShowDeleteCategoryModal(false)
              },
              {
                type: 'button',
                label: '确定',
                level: 'danger',
                actionType: 'confirm',
                onClick: confirmDeleteCategory
              }
            ]
          }}
          onClose={() => setShowDeleteCategoryModal(false)}
        />
      )}

      {/* 右键菜单 */}
      {rightMenuVisible && rightMenuItem && (
        <div
          className="customContextMenu"
          style={{
            left: rightMenuPosition.x + 'px',
            top: rightMenuPosition.y + 'px'
          }}
        >
          <div
            className="customContextMenu-item"
            onClick={() => {
              handleRenameCategory(rightMenuItem);
              setRightMenuVisible(false);
            }}
          >
            <div className="customContextMenu-item-name">重命名</div>
          </div>
          <div
            className="customContextMenu-item"
            onClick={() => handleDeleteCategory(rightMenuItem)}
          >
            <div className="customContextMenu-item-name">删除分类</div>
          </div>
        </div>
      )}

      {/* 创建组件对话框 */}
      {showCreateComponentModal && (
        <AMISRenderer
          show={showCreateComponentModal}
          schema={{
            type: 'dialog',
            title: '新建应用组件',
            body: {
              type: 'form',
              api: {
                method: 'post',
                url: '/admin-api/system/component/create',
                data: {
                  name: '${name}',
                  componentKey: '${componentKey}',
                  icon: iconValue,
                  classificationId: '${classificationId}',
                  description: '${description}',
                  status: '${status ? 1 : 0}',
                  type: 3,
                  applicationId: applicationId
                },
                adaptor: function (
                  payload: any,
                  response: any,
                  api: any,
                  context: any
                ) {
                  setShowCreateComponentModal(false);
                  setAmisKey(Date.now());
                  return payload;
                }
              },
              body: [
                {
                  type: 'input-text',
                  name: 'name',
                  label: '组件名称',
                  required: true
                },
                {
                  type: 'input-text',
                  name: 'componentKey',
                  label: '组件标识',
                  required: true,
                  placeholder: '请输入组件唯一标识'
                },
                {
                  type: 'select',
                  name: 'classificationId',
                  label: '分类',
                  options: categories
                    .filter(category => category.id !== 'all')
                    .map(category => ({
                      label: category.name,
                      value: category.id
                    }))
                },
                {
                  type: 'textarea',
                  name: 'description',
                  label: '组件描述'
                },
                {
                  type: 'switch',
                  name: 'status',
                  label: '状态',
                  value: true
                }
              ]
            },
            actions: [
              {
                type: 'button',
                label: '取消',
                actionType: 'cancel'
              },
              {
                type: 'button',
                label: '确定',
                level: 'primary',
                actionType: 'submit'
              }
            ]
          }}
          onClose={() => setShowCreateComponentModal(false)}
        />
      )}
    </div>
  );
};

export default SystemComponent;
export {getSystemComponentConfigs};
