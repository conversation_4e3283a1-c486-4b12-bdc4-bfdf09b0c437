import React from 'react';
import {Render<PERSON>, RendererProps} from 'amis';
import {FormControlProps, CustomStyle, setThemeClassName} from 'amis-core';
import './MyNav.scss';
import {SpinnerExtraProps} from 'amis-ui';
import {BadgeObject} from 'amis-ui';
import {updateFormData, UpdateFormDataParams} from '@/utils/api/api';
import {classnames as cx} from 'amis-core';

export type NavItemSchema = {
  /**
   * 文字说明
   */
  label?: string;
  /**
   * 图标类名，参考 fontawesome 4。
   */
  icon?: string | Array<{icon?: string; position: string}>;
  /**
   * 链接地址
   */
  to?: string;
  /**
   * 链接打开方式
   */
  target?: string;
  /**
   * 是否展开
   */
  unfolded?: boolean;
  /**
   * 是否激活
   */
  active?: boolean;
  /**
   * 是否懒加载
   */
  defer?: boolean;
  /**
   * 懒加载API
   */
  deferApi?: string;
  /**
   * 子菜单
   */
  children?: Array<NavItemSchema>;
  /**
   * 唯一标识
   */
  key?: string;
  /**
   * 是否禁用
   */
  disabled?: boolean;
  /**
   * 禁用提示
   */
  disabledTip?: string;
  /**
   * 自定义类名
   */
  className?: string;
  /**
   * 菜单模式
   */
  mode?: string;
};

export interface NavOverflow {
  /**
   * 是否开启响应式收纳
   */
  enable: boolean;
  /**
   * 菜单触发按钮的文字
   */
  overflowLabel?: string;
  /**
   * 菜单触发按钮的图标
   * @default "fa fa-ellipsis-h"
   */
  overflowIndicator?: string;
  /**
   * 菜单触发按钮CSS类名
   */
  overflowClassName?: string;
  /**
   * Popover浮层CSS类名
   */
  overflowPopoverClassName?: string;
  /**
   * 菜单外层CSS类名
   */
  overflowListClassName?: string;
  /**
   * 导航横向布局时，开启开启响应式收纳后最大可显示数量，超出此数量的导航将被收纳到下拉菜单中
   */
  maxVisibleCount?: number;
  /**
   * 包裹导航的外层标签名，可以使用其他标签渲染
   * @default "ul"
   */
  wrapperComponent?: string;
  /**
   * 导航项目宽度
   * @default 160
   */
  itemWidth?: number;
  /**
   * 导航列表后缀节点
   */
  overflowSuffix?: any;
  /**
   * 自定义样式
   */
  style?: React.CSSProperties;
  /**
   * 导航超出后响应式收纳方案。
   * @default "popup"
   * popup 导航被收纳到下拉菜单中
   * swipe 导航展示在一个可左右滑动的菜单中，通过左右箭头滚动查看。只在横向布局有效
   */
  mode?: 'popup' | 'swipe';
}

export interface MyNavProps extends RendererProps {
  /**
   * 指定为 my-nav 类型
   */
  type?: 'my-nav';

  /**
   * 导航头部配置
   */
  header?: {
    /**
     * 是否开启导航头部
     */
    enable?: boolean;
    /**
     * 是否显示标题
     */
    showTitle?: boolean;
    /**
     * 导航头部标题
     */
    title?: string;
    /**
     * 是否显示搜索
     */
    showSearch?: boolean;
    /**
     * 是否显示新增父级导航
     */
    // showAddParent?: boolean;
    /**
     * 是否显示新增导航
     */
    showAddNav?: boolean;
    /**
     * 是否显示收起导航
     */
    showCollapse?: boolean;
    /**
     * 导航头部自定义类名
     */
    className?: string;
    /**
     * 导航头部自定义样式
     */
    style?: React.CSSProperties;
  };

  /**
   * 链接地址集合
   */
  links?: Array<NavItemSchema>;

  /**
   * 数据变化回调
   */
  onChange?: (value: any, name?: string) => void;

  /**
   * 缩进大小
   * @default 16
   */
  indentSize?: number;

  /**
   * 可以通过 API 拉取。
   */
  source?: string | any;

  /**
   * 懒加载 api，如果不配置复用 source 接口。
   */
  deferApi?: string;

  /**
   * true 为垂直排列，false 为水平排列类似如 tabs。
   */
  stacked?: boolean;

  /**
   * 更多操作菜单列表
   */
  itemActions?: any;

  /**
   * 可拖拽
   */
  draggable?: boolean;

  /**
   * 保存排序的 api
   */
  saveOrderApi?: string;

  /**
   * 角标
   */
  itemBadge?: BadgeObject;

  /**
   * 角标
   */
  badge?: BadgeObject;

  /**
   * 仅允许同层级拖拽
   */
  dragOnSameLevel?: boolean;

  /**
   * 横向导航时自动收纳配置
   */
  overflow?: NavOverflow;

  /**
   * 最多展示多少层级
   */
  level?: number;

  /**
   * 默认展开层级 小于等于该层数的节点默认全部打开
   */
  defaultOpenLevel?: number;

  /**
   * 控制仅展示指定key菜单下的子菜单项
   */
  showKey?: string;

  /**
   * 控制菜单缩起
   */
  collapsed?: boolean;

  /**
   * 垂直模式 非折叠状态下 控制菜单打开方式
   */
  mode?: 'panel' | 'float' | 'inline';

  /**
   * 自定义展开图标
   */
  expandIcon?: string;

  /**
   * 自定义展开图标位置 默认在前面 before after
   */
  expandPosition?: string;

  /**
   * 主题配色 默认light
   */
  themeColor?: 'light' | 'dark';

  /**
   * 手风琴展开 仅垂直inline模式支持
   */
  accordion?: boolean;

  /**
   * 子菜单项展开浮层样式
   */
  popupClassName?: string;

  /**
   * 是否开启搜索
   */
  searchable?: boolean;

  /**
   * 搜索框相关配置
   */
  searchConfig?: {
    /**
     * 搜索框外层CSS样式类
     */
    className?: string;
    /**
     * 搜索匹配函数
     */
    matchFunc?: string | any;
    /**
     * 占位符
     */
    placeholder?: string;
    /**
     * 是否为 Mini 样式。
     */
    mini?: boolean;
    /**
     * 是否为加强样式
     */
    enhance?: boolean;
    /**
     * 是否可清除
     */
    clearable?: boolean;
    /**
     * 是否立马搜索。
     */
    searchImediately?: boolean;
    /**
     * 指定唯一标识字段
     */
    valueField?: string;
  };

  /**
   * 是否可见
   */
  visible?: boolean;
  /**
   * 是否隐藏
   */
  hidden?: boolean;

  /**
   * CSS类名
   */
  className?: string;

  /**
   * 自定义样式
   */
  style?: React.CSSProperties;
}

@Renderer({
  type: 'my-nav',
  name: 'my-nav'
})
export class MyNavRenderer extends React.Component<MyNavProps> {
  static defaultProps: Partial<MyNavProps> = {
    className: '',
    indentSize: 16,
    stacked: false,
    mode: 'inline',
    themeColor: 'light'
  };

  // 已移除parseThemeCss方法，现在使用amis内置的setThemeClassName和CustomStyle
  // parseThemeCss = (themeCss: any, className: string) => {
  //   return {};
  // };

  state = {
    searchExpanded: false,
    navCollapsed: false,
    showAddDialog: false,
    showEditDialog: false,
    editingItem: null as any,
    localLinks: null as any[] | null,  // 本地链接数据缓存
    transformedLinks: null as any[] | null  // 转换后的API数据缓存
  };

  // 组件挂载后添加点击事件监听
  componentDidMount() {
    this.addNavClickListener();
  }

  // 组件更新后处理属性变化
  componentDidUpdate(prevProps: any) {
    const { itemActions, enableItemActions } = this.props as any;
    const { itemActions: prevItemActions, enableItemActions: prevEnableItemActions } = prevProps;

    // 检查enableItemActions是否发生变化（这是最重要的）
    const enableChanged = enableItemActions !== prevEnableItemActions;

    // 检查itemActions数组长度是否发生变化
    const itemActionsLengthChanged =
      (itemActions?.length || 0) !== (prevItemActions?.length || 0);

    // 检查itemActions内容是否发生变化（简单的JSON比较）
    const itemActionsContentChanged =
      JSON.stringify(itemActions || []) !== JSON.stringify(prevItemActions || []);

    if (enableChanged || itemActionsLengthChanged || itemActionsContentChanged) {
      // 强制重新渲染
      this.forceUpdate();
    }
  }

  // 组件卸载前移除事件监听
  componentWillUnmount() {
    this.removeNavClickListener();
  }

  // 添加导航点击事件监听
  addNavClickListener = () => {
    // 使用事件委托监听导航项点击
    document.addEventListener('click', this.handleNavItemClick, true);
  };

  // 移除导航点击事件监听
  removeNavClickListener = () => {
    document.removeEventListener('click', this.handleNavItemClick, true);
  };

  // 处理导航项点击事件
  handleNavItemClick = (event: MouseEvent) => {
    const target = event.target as HTMLElement;

    // 查找最近的导航项元素
    const navItem = target.closest('.cxd-Nav-item');
    if (!navItem) {
      // 尝试其他可能的选择器
      const navItemAlt = target.closest('[data-role="nav-item"]') ||
                        target.closest('.cxd-Nav-Menu-item') ||
                        target.closest('a[href]');
      if (!navItemAlt) return;
    }

    // 获取导航项的数据
    const navLink = navItem ? navItem.querySelector('a') : target.closest('a');
    if (!navLink) return;

    // 检查是否是需要新窗口打开的链接
    const href = navLink.getAttribute('href');
    if (!href) return;

    // 查找对应的菜单数据
    const menuData = this.findMenuByHref(href);

    if (menuData && menuData.blank) {
      // 阻止默认行为
      event.preventDefault();
      event.stopPropagation();

      // 在新窗口打开
      window.open(menuData.to, '_blank');
    } else {
    }
  };

  // 转换API数据格式为组件期望的格式
  transformApiData = (apiData: any[]): any[] => {
    if (!apiData || !Array.isArray(apiData)) {
      return [];
    }

    const transformItem = (item: any): any => {
      // 只处理状态为1（启用）的菜单项
      if (item.status !== 1) {
        return null;
      }

      const transformed: any = {
        label: item.name || '',
        key: item.id?.toString() || Date.now().toString(),
        id: item.id?.toString() || Date.now().toString(),
        disabled: false,
        active: false,
        unfolded: false
      };

      // 设置图标
      if (item.icon) {
        transformed.icon = item.icon;
      } else {
        // 根据类型设置默认图标
        transformed.icon = item.type === 2 ? 'fa fa-folder' : 'fa fa-file-o';
      }

      // 设置跳转地址
      if (item.url) {
        transformed.to = item.url;
        transformed.target = '_self';
      } else if (item.type === 1) {
        // 页面类型，构建正确的路由格式: /app{applicationId}/admin/page{id}
        transformed.to = `/app${item.applicationId}/admin/page${item.id}`;
        transformed.target = '_self';
      }

      // 处理子菜单
      if (item.children && Array.isArray(item.children) && item.children.length > 0) {
        const transformedChildren = item.children
          .map(transformItem)
          .filter((child: any) => child !== null);

        if (transformedChildren.length > 0) {
          transformed.children = transformedChildren;
        }
      }

      return transformed;
    };

    return apiData
      .map(transformItem)
      .filter(item => item !== null);
  };

  // 根据href查找对应的菜单数据
  findMenuByHref = (href: string): any => {
    const { links = [] } = this.props as any;
    const currentLinks = this.state.localLinks || this.state.transformedLinks || links;

    const findInLinks = (items: any[]): any => {
      for (const item of items) {
        if (item.to && href.includes(item.to)) {
          return item;
        }
        if (item.children && item.children.length > 0) {
          const found = findInLinks(item.children);
          if (found) return found;
        }
      }
      return null;
    };

    const result = findInLinks(currentLinks);
    return result;
  };

  // 切换搜索功能
  toggleSearch = () => {
    // 切换搜索功能的启用状态
    this.setState({
      searchExpanded: !this.state.searchExpanded
    });
    
    // 延迟聚焦搜索框
    setTimeout(() => {
      const searchInput = document.querySelector('.cxd-Nav-searchbox .cxd-TextControl input');
      if (searchInput && this.state.searchExpanded) {
        (searchInput as HTMLInputElement).focus();
      }
    }, 100);
  };



  // 处理新增导航点击
  handleAddNav = () => {
    // 设置显示对话框状态
    this.setState({ showAddDialog: true });
  };

  // 关闭添加对话框
  handleCloseAddDialog = () => {
    this.setState({ showAddDialog: false });
  };

  // 渲染添加对话框
  renderAddDialog() {
    const { render } = this.props as any;
    const { showAddDialog } = this.state;

    if (!showAddDialog || !render) {
      return null;
    }

    const dialogSchema = this.createAddMenuDialog();
    return render('dialog', {
      ...dialogSchema,
      show: showAddDialog,
      onClose: this.handleCloseAddDialog,
      onCancel: this.handleCloseAddDialog
    });
  }

  // 渲染编辑对话框
  renderEditDialog() {
    const { render } = this.props as any;
    const { showEditDialog, editingItem } = this.state;

    if (!showEditDialog || !render || !editingItem) {
      return null;
    }

    const dialogSchema = this.createEditMenuDialog(editingItem);
    return render('dialog', {
      ...dialogSchema,
      show: showEditDialog,
      onClose: this.handleCloseEditDialog,
      onCancel: this.handleCloseEditDialog
    });
  }

  // 构建树形选项
  buildTreeOptions = (menuItems: any[]) => {
    const buildOptions = (items: any[]): any[] => {
      return items.map(item => ({
        label: item.label,
        value: item.label || item.to,
        icon: item.icon || 'fa fa-file-o',
        children: item.children && item.children.length > 0 ? buildOptions(item.children) : undefined
      }));
    };

    return buildOptions(menuItems);
  };

  // 创建添加菜单项对话框的schema
  createAddMenuDialog = () => {
    const { links = [] } = this.props as any;
    // 使用最新的链接数据（本地状态优先）
    const currentLinks = this.state.localLinks || links;

    // 构建父级菜单选项
    const parentOptions = [
      { label: '请选择，不选择默认为一级菜单', value: '' }
    ];

    // 递归添加菜单项作为父级选项，显示层级结构
    const addMenuOptions = (menuItems: any[], level = 0) => {
      menuItems.forEach(item => {
        // 使用图标和缩进来显示层级
        const indent = '　'.repeat(level);
        const icon = item.icon ? `<i class="${item.icon}"></i> ` : '<i class="fa fa-file-o"></i> ';

        parentOptions.push({
          label: `${indent}${icon}${item.label}`,
          value: item.label || item.to
        } as any);

        if (item.children && item.children.length > 0) {
          addMenuOptions(item.children, level + 1);
        }
      });
    };

    addMenuOptions(currentLinks);

    return {
      type: 'dialog',
      title: '添加菜单项',
      size: 'm',
      closeOnEsc: true,
      closeOnOutside: false,
      showCloseButton: true,
      body: {
        type: 'form',
        mode: 'horizontal',
        labelAlign: 'right',
        submitText: '确认',
        onSubmit: (values: any) => {
          this.handleAddMenuConfirm(values);
        },
        body: [
          {
            type: 'input-text',
            name: 'label',
            label: '菜单名称',
            required: true,
            placeholder: '请输入菜单名称'
          },
          {
            type: 'tree-select',
            name: 'parentMenu',
            label: '父级菜单',
            placeholder: '请选择，不选择默认为一级菜单',
            options: this.buildTreeOptions(currentLinks),
            clearable: true,
            showIcon: true,
            cascade: false,
            withChildren: false,
            onlyChildren: false
          },
          {
            type: 'icon-select',
            name: 'icon',
            label: '菜单图标',
            placeholder: '点击选择图标',
          },
          {
            type: 'input-text',
            name: 'to',
            label: '跳转地址',
            placeholder: '请输入地址',
          },
          {
            type: 'radios',
            name: 'openType',
            label: '跳转方式',
            value: 'current',
            options: [
              { label: '当前页展开', value: 'current' },
              { label: '新标签页打开', value: 'blank' }
            ]
          },
          {
            type: 'input-text',
            name: 'description',
            label: '角标内容',
            placeholder: '若为空则不展示角标',
            maxRows: 3
          }
        ]
      },
      actions: [
        {
          type: 'button',
          label: '取消',
          actionType: 'cancel',
          onClick: this.handleCloseAddDialog
        },
        {
          type: 'button',
          label: '确认',
          level: 'primary',
          actionType: 'submit'
        }
      ]
    };
  };

  // 处理编辑菜单项
  handleEditMenuItem = (item: any) => {
    this.setState({
      showEditDialog: true,
      editingItem: item
    });
  };

  // 处理删除菜单项
  handleDeleteMenuItem = async (item: any) => {
    const { links = [] } = this.props as any;
    const currentLinks = this.state.localLinks || links;

    // 递归删除菜单项
    const deleteFromLinks = (items: any[]): any[] => {
      return items.filter(linkItem => {
        if (linkItem.key === item.key || linkItem.id === item.id) {
          return false; // 删除匹配的项
        }
        if (linkItem.children && linkItem.children.length > 0) {
          linkItem.children = deleteFromLinks(linkItem.children);
        }
        return true;
      });
    };

    const newLinks = deleteFromLinks(currentLinks);

    // 调用API保存数据
    await this.saveNavDataToAPI(newLinks);

    // 更新本地数据
    this.updateNavLinks(newLinks);

  };

  // 关闭编辑对话框
  handleCloseEditDialog = () => {
    this.setState({
      showEditDialog: false,
      editingItem: null
    });
  };

  // 创建编辑菜单项对话框的schema
  createEditMenuDialog = (editingItem: any) => {
    const { links = [] } = this.props as any;
    const currentLinks = this.state.localLinks || links;

    return {
      type: 'dialog',
      title: '编辑菜单项',
      size: 'md',
      body: {
        type: 'form',
        api: {
          method: 'post',
          url: '/api/nav/update',
          requestAdaptor: (api: any) => {
            // 这里可以添加请求适配逻辑
            return api;
          }
        },
        onFinished: this.handleEditMenuConfirm,
        body: [
          {
            type: 'input-text',
            name: 'label',
            label: '菜单名称',
            required: true,
            placeholder: '请输入菜单名称',
            value: editingItem.label
          },
          {
            type: 'tree-select',
            name: 'parentMenu',
            label: '父级菜单',
            placeholder: '请选择，不选择默认为一级菜单',
            options: this.buildTreeOptions(currentLinks),
            clearable: true,
            showIcon: true,
            cascade: false,
            withChildren: false,
            onlyChildren: false
          },
          {
            type: 'icon-select',
            name: 'icon',
            label: '菜单图标',
            placeholder: '点击选择图标',
            value: editingItem.icon
          },
          {
            type: 'input-text',
            name: 'to',
            label: '跳转地址',
            placeholder: '请输入地址',
            value: editingItem.to
          },
          {
            type: 'radios',
            name: 'openType',
            label: '跳转方式',
            value: editingItem.target === '_blank' ? 'blank' : 'current',
            options: [
              { label: '当前页展开', value: 'current' },
              { label: '新标签页打开', value: 'blank' }
            ]
          },
          {
            type: 'input-text',
            name: 'description',
            label: '角标内容',
            placeholder: '若为空则不展示角标',
            value: editingItem.badge?.text || '',
            maxRows: 3
          }
        ]
      },
      actions: [
        {
          type: 'button',
          label: '取消',
          actionType: 'cancel',
          onClick: this.handleCloseEditDialog
        },
        {
          type: 'button',
          label: '确认',
          level: 'primary',
          actionType: 'submit'
        }
      ]
    };
  };

  // 处理编辑菜单确认
  handleEditMenuConfirm = async (values: any) => {
    const { links = [] } = this.props as any;
    const currentLinks = this.state.localLinks || links;
    const { editingItem } = this.state;

    if (!editingItem) return;

    // 创建更新后的菜单项
    const updatedNavItem: any = {
      ...editingItem,
      label: values.label,
      icon: values.icon || '',
      to: values.to || '',
      target: values.openType === 'blank' ? '_blank' : '_self',
      // 更新角标
      ...(values.description && values.description.trim() ? {
        badge: {
          text: values.description,
          mode: 'text'
        }
      } : { badge: undefined })
    };

    // 递归更新菜单项
    const updateInLinks = (items: any[]): any[] => {
      return items.map(item => {
        if (item.key === editingItem.key || item.id === editingItem.id) {
          return updatedNavItem;
        } else if (item.children && item.children.length > 0) {
          return {
            ...item,
            children: updateInLinks(item.children)
          };
        }
        return item;
      });
    };

    const newLinks = updateInLinks(currentLinks);

    // 调用API保存数据
    await this.saveNavDataToAPI(newLinks);

    // 更新本地数据
    this.updateNavLinks(newLinks);

    // 关闭对话框
    this.handleCloseEditDialog();

  };

  // 处理添加菜单确认
  handleAddMenuConfirm = async (values: any) => {
    const { links = [] } = this.props as any;
    // 使用最新的链接数据（本地状态优先）
    const currentLinks = this.state.localLinks || links;

    // 创建新的菜单项，确保包含所有必要属性
    const newNavItem: any = {
      label: values.label,
      icon: values.icon || '',
      children: [],
      key: Date.now().toString(), // 唯一标识
      id: Date.now().toString(),  // 兼容性ID
      // 添加角标支持
      ...(values.description && values.description.trim() ? {
        badge: {
          text: values.description,
          mode: 'text'
        }
      } : {}),
      // 确保链接可点击
      disabled: false,
      active: false,
      unfolded: false
    };

    // 根据跳转方式设置不同的属性
    if (values.openType === 'blank') {
      // 新标签页打开：使用 to 和 blank 属性
      newNavItem.to = values.to || '';
      newNavItem.blank = true;  // amis 支持的新标签页属性
      newNavItem.target = '_blank';
    } else {
      // 当前页面打开：使用 to 属性
      newNavItem.to = values.to || '';
      newNavItem.target = '_self';
    }

    let newLinks = [...currentLinks];

    // 如果选择了父级菜单，添加为子菜单
    if (values.parentMenu) {
      const addToParent = (items: any[]): any[] => {
        return items.map(item => {
          if (item.label === values.parentMenu || item.to === values.parentMenu) {
            return {
              ...item,
              children: [...(item.children || []), newNavItem]
            };
          } else if (item.children && item.children.length > 0) {
            return {
              ...item,
              children: addToParent(item.children)
            };
          }
          return item;
        });
      };
      newLinks = addToParent(newLinks);
    } else {
      // 添加为一级菜单
      newLinks.push(newNavItem);
    }

    // 调用API保存数据
    await this.saveNavDataToAPI(newLinks);

    // 更新本地数据
    this.updateNavLinks(newLinks);

    // 关闭对话框
    this.handleCloseAddDialog();

  };

  // 保存导航数据到API
  saveNavDataToAPI = async (newLinks: any[]) => {
    try {
      // 从环境或props中获取API相关信息
      const { env, data } = this.props as any;


      // 获取当前页面的完整数据结构
      const currentPageData = this.getCurrentPageData();

      // 更新导航数据 - 创建全新的对象避免修改原始数据
      const updatedPageData = JSON.parse(JSON.stringify({
        ...currentPageData,
        body: (currentPageData.body || []).map((item: any) => {
          if (item.type === 'my-nav') {
            return {
              ...item,
              links: newLinks
            };
          }
          return { ...item };
        })
      }));

      // 构建API请求参数
      const apiParams: UpdateFormDataParams = {
        id: data?.formDataId || 1769, // 从data中获取ID，如果没有则使用默认值
        applicationPageId: data?.applicationPageId || 1735, // 从data中获取页面ID
        data: JSON.stringify(updatedPageData),
        field: ""
      };


      // 使用项目中的 updateFormData API 方法
      const result = await updateFormData(apiParams);
    } catch (error) {
    }
  };

  // 获取当前页面数据结构
  getCurrentPageData = () => {
    const { data, ...restProps } = this.props as any;

    // 尝试从不同来源获取页面数据
    let pageData = null;

    // 方式1: 从data中获取
    if (data && data.pageData) {
      pageData = data.pageData;
    }

    // 方式2: 从props中获取
    if (!pageData && data && typeof data === 'object') {
      pageData = data;
    }

    // 构建默认的页面数据结构
    const defaultPageData = {
      id: "u:542d074e283f",
      pullRefresh: { disabled: true },
      type: "page",
      title: "45",
      regions: ["body"],
      body: [
        {
          type: "my-nav",
          stacked: restProps.stacked !== undefined ? restProps.stacked : true,
          links: restProps.links || [],
          id: restProps.id || "u:6c2453eba92d",
          searchable: restProps.searchable || false,
          draggable: restProps.draggable || false,
          collapsed: restProps.collapsed || false,
          style: restProps.style || { fontFamily: "", fontSize: 12 },
          header: restProps.header || {
            enable: true,
            showTitle: true,
            showSearch: false,
            showAddNav: true,
            showCollapse: false,
            title: "导航菜单"
          }
        }
      ],
      asideResizor: false
    };

    // 如果没有获取到有效的页面数据，使用默认数据
    if (!pageData) {
      pageData = defaultPageData;
    } else {
      // 创建一个新的可修改对象，避免修改原始对象
      pageData = {
        ...pageData,
        body: pageData.body && Array.isArray(pageData.body) ? pageData.body : defaultPageData.body
      };
    }

    return pageData;
  };

  // 更新导航链接数据
  updateNavLinks = (newLinks: any[]) => {
    const { onChange, name, dispatchEvent } = this.props as any;

    // 首先更新本地状态，确保立即重新渲染
    this.setState({ localLinks: newLinks });

    // 方式1: 通过onChange回调更新
    if (onChange && name) {
      try {
        onChange(newLinks, name);
        return;
      } catch (e) {
      }
    }

    // 方式2: 通过dispatchEvent触发事件
    if (dispatchEvent) {
      try {
        dispatchEvent('change', {
          data: {
            [name || 'links']: newLinks
          }
        });
        return;
      } catch (e) {
      }
    }

    // 方式3: 触发自定义事件
    try {
      const event = new CustomEvent('nav-add-item', {
        detail: { newLinks }
      });
      window.dispatchEvent(event);
    } catch (e) {
    }
  };

  // 处理收起导航点击
  handleCollapse = () => {
    this.setState({ navCollapsed: !this.state.navCollapsed });
  };

  // 渲染导航头部
  renderNavHeader() {
    const { header, themeCss, id } = this.props;
    const { navCollapsed } = this.state;

    if (!header || !header.enable) {
      return null;
    }

    // 确保头部有默认样式
    const defaultHeaderStyle = {
      minHeight: '48px',
      display: 'flex',
      alignItems: 'center',
      width: '100%',
      ...header.style
    };

    // 移除手动CSS变量处理，使用amis标准样式系统

    return (
      <div
        className={cx(
          'nav-header',
          header.className,
          setThemeClassName({
            name: 'headerControlClassName',
            id,
            themeCss,
            ...this.props
          })
        )}
        style={defaultHeaderStyle}
      >
        <div className="nav-header-content">
          {/* 标题部分 */}
          {header.showTitle && header.title && (
            <div className="nav-header-title">
              {this.props.render('header-title', {
                type: 'tpl',
                tpl: header.title,
                className: cx(
                  setThemeClassName({
                    name: 'headerTitleControlClassName',
                    id,
                    themeCss,
                    ...this.props
                  })
                )
              })}
            </div>
          )}

          {/* 右侧操作区 */}
          <div className="nav-header-actions">
            {/* 搜索功能 */}
            {header.showSearch && (
              <button
                className={cx(
                  "search-btn",
                  setThemeClassName({
                    name: 'navBtnControlClassName',
                    id,
                    themeCss,
                    ...this.props
                  })
                )}
                onClick={this.toggleSearch}
                title="搜索"
              >
                <svg
                  viewBox="0 0 16 16"
                  xmlns="http://www.w3.org/2000/svg"
                  className="nav-icon search-icon"
                >
                  <g fill="none" fillRule="evenodd" strokeLinecap="round" strokeLinejoin="round">
                    <circle cx="7.5" cy="7.5" r="6"/>
                    <path d="m11.879 11.879 2.828 2.828"/>
                  </g>
                </svg>
              </button>
            )}

            {/* 新增导航 */}
            {header.showAddNav && (
              <button
                className={cx(
                  "add-nav-btn",
                  setThemeClassName({
                    name: 'navBtnControlClassName',
                    id,
                    themeCss,
                    ...this.props
                  })
                )}
                onClick={this.handleAddNav}
                title="新增导航"
              >
                <svg
                  viewBox="0 0 16 16"
                  xmlns="http://www.w3.org/2000/svg"
                  className="nav-icon add-icon"
                >
                  <g strokeLinejoin="round" fill="none" fillRule="evenodd" strokeLinecap="round">
                    <path d="M2.5 8h11M8 2.5v11"/>
                  </g>
                </svg>
              </button>
            )}

            {/* 收起导航 */}
            {header.showCollapse && (
              <button
                className={cx(
                  "collapse-btn",
                  navCollapsed ? 'collapsed' : '',
                  setThemeClassName({
                    name: 'navBtnControlClassName',
                    id,
                    themeCss,
                    ...this.props
                  })
                )}
                onClick={this.handleCollapse}
                title={navCollapsed ? "展开导航" : "收起导航"}
              >
                <svg
                  viewBox="0 0 16 16"
                  xmlns="http://www.w3.org/2000/svg"
                  className="nav-icon collapse-icon"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" d="m2.35 5.497 5.657 5.657 5.657-5.657" fill="none" fillRule="evenodd"/>
                </svg>
              </button>
            )}
          </div>
        </div>
      </div>
    );
  }

  render() {
    const {
      render,
      header,
      clearable,
      placeholder,
      draggable,
      saveOrderApi,
      dragOnSameLevel,
      mini,
      enhance,
      searchable,
      searchConfig,
      matchFunc,
      searchImediately,
      accordion,
      stacked,
      mode,
      defaultOpenLevel,
      level,
      collapsed,
      expandIcon,
      expandPosition,
      themeColor,
      popupClassName,
      links,
      source,
      deferApi,
      itemBadge,
      badge,
      overflow,
      showKey,
      style,        // 提取样式属性
      className,    // 提取类名属性
      themeCss,     // 提取主题CSS配置
      id,           // 提取组件ID
      env,          // 提取环境配置
      ...rest
    } = this.props;

    // 移除手动CSS变量处理，使用amis标准样式系统

    // 直接将所有属性传递给原生nav组件
    if (render) {
      // 确保 mini 和 enhance 属性被正确地传递到 searchConfig 对象中
      const mergedSearchConfig = searchable || this.state.searchExpanded
        ? {
            ...searchConfig,
            clearable: clearable !== undefined ? clearable : true,
            placeholder: placeholder || '请输入关键词',
            mini: mini !== undefined ? mini : searchConfig?.mini,
            enhance: enhance !== undefined ? enhance : searchConfig?.enhance,
            matchFunc: matchFunc !== undefined ? matchFunc : searchConfig?.matchFunc,
            searchImediately: searchImediately !== undefined ? searchImediately : searchConfig?.searchImediately,
            // 添加搜索输入框的样式类名
            className: cx(
              searchConfig?.className,
              setThemeClassName({
                name: 'searchInputClassName',
                id,
                themeCss,
                ...this.props
              })
            ),
            // 为input元素添加专门的样式类名
            inputControlClassName: cx(
              setThemeClassName({
                name: 'searchInputClassName',
                id,
                themeCss,
                ...this.props
              })
            ),
            // 直接传递themeCss给搜索组件，确保字体样式生效
            themeCss: {
              inputControlClassName: themeCss?.searchInputClassName
            },
            // 添加内联样式作为备用方案
            style: (() => {
              const searchStyles = themeCss?.searchInputClassName;
              if (searchStyles) {
                const style: any = {};

                // 提取字体相关样式
                if (searchStyles['--color-default']) {
                  style['--search-text-color'] = searchStyles['--color-default'];
                }
                if (searchStyles['--font-size-default']) {
                  style['--search-font-size'] = searchStyles['--font-size-default'];
                }
                if (searchStyles['--font-weight-default']) {
                  style['--search-font-weight'] = searchStyles['--font-weight-default'];
                }
                if (searchStyles['--line-height-default']) {
                  style['--search-line-height'] = searchStyles['--line-height-default'];
                }

                return style;
              }
              return {};
            })()
          }
        : undefined;

      // 如果点击了搜索按钮，启用搜索功能
      const finalSearchable = searchable || this.state.searchExpanded;

      // 获取最终的链接数据
      let finalLinksData = null;

      // 优先使用本地状态的链接数据
      if (this.state.localLinks) {
        finalLinksData = this.state.localLinks;
      }
      // 如果有转换后的API数据，使用转换后的数据
      else if (this.state.transformedLinks) {
        finalLinksData = this.state.transformedLinks;
      }
      // 最后使用props中的links
      else {
        finalLinksData = links;
      }

      // 为每个导航项添加样式类名
      const addNavItemClassName = (items: any[]): any[] => {
        if (!items) return items;

        return items.map(item => ({
          ...item,
          className: cx(
            item.className,
            setThemeClassName({
              name: 'navItemClassName',
              id,
              themeCss,
              ...this.props
            })
          ),
          children: item.children ? addNavItemClassName(item.children) : undefined
        }));
      };

      const finalLinks = finalLinksData ? addNavItemClassName(finalLinksData) : finalLinksData;

      // 处理source数据转换
      let processedSource = source;

      // 如果没有finalLinksData且有source，则需要配置数据转换
      if (source && !finalLinksData) {
        if (typeof source === 'string') {
        // 如果使用API数据源，配置数据转换
        processedSource = {
          url: source,
          method: 'get',
          adaptor: `
            // 转换API返回的数据格式
            const transformItem = (item) => {
              if (item.status !== 1) return null;

              const transformed = {
                label: item.name || '',
                key: item.id?.toString() || Date.now().toString(),
                id: item.id?.toString() || Date.now().toString(),
                disabled: false,
                active: false,
                unfolded: false
              };

              if (item.icon) {
                transformed.icon = item.icon;
              } else {
                transformed.icon = item.type === 2 ? 'fa fa-folder' : 'fa fa-file-o';
              }

              if (item.url) {
                transformed.to = item.url;
                transformed.target = '_self';
              } else if (item.type === 1) {
                // 页面类型，构建正确的路由格式: /app{applicationId}/admin/page{id}
                transformed.to = '/app' + item.applicationId + '/admin/page' + item.id;
                transformed.target = '_self';
              }

              if (item.children && Array.isArray(item.children) && item.children.length > 0) {
                const transformedChildren = item.children.map(transformItem).filter(child => child !== null);
                if (transformedChildren.length > 0) {
                  transformed.children = transformedChildren;
                }
              }

              return transformed;
            };

            if (payload.data && Array.isArray(payload.data)) {
              payload.data = payload.data.map(transformItem).filter(item => item !== null);
            }

            return payload;
          `
        };
        } else if (typeof source === 'object' && source.url) {
          // 如果source已经是对象，添加adaptor
          processedSource = {
            ...source,
            adaptor: `
              // 转换API返回的数据格式
              const transformItem = (item) => {
                if (item.status !== 1) return null;

                const transformed = {
                  label: item.name || '',
                  key: item.id?.toString() || Date.now().toString(),
                  id: item.id?.toString() || Date.now().toString(),
                  disabled: false,
                  active: false,
                  unfolded: false
                };

                if (item.icon) {
                  transformed.icon = item.icon;
                } else {
                  transformed.icon = item.type === 2 ? 'fa fa-folder' : 'fa fa-file-o';
                }

                if (item.url) {
                  transformed.to = item.url;
                  transformed.target = '_self';
                } else if (item.type === 1) {
                  // 页面类型，构建正确的路由格式: /app{applicationId}/admin/page{id}
                  transformed.to = '/app' + item.applicationId + '/admin/page' + item.id;
                  transformed.target = '_self';
                }

                if (item.children && Array.isArray(item.children) && item.children.length > 0) {
                  const transformedChildren = item.children.map(transformItem).filter(child => child !== null);
                  if (transformedChildren.length > 0) {
                    transformed.children = transformedChildren;
                  }
                }

                return transformed;
              };

              if (payload.data && Array.isArray(payload.data)) {
                payload.data = payload.data.map(transformItem).filter(item => item !== null);
              }

              return payload;
            `
          };
        }
      }

      // 构建菜单项操作按钮
      const { itemActions, enableItemActions } = this.props as any;
      let menuItemActions = null;

      // 只有在明确开启操作栏时才显示操作按钮
      if (enableItemActions === true) {
        if (itemActions && Array.isArray(itemActions)) {
          // 处理自定义的操作按钮配置
          menuItemActions = itemActions.map((action: any) => {
            const processedAction = { ...action };

            // 为编辑和删除操作添加事件处理
            if (action.label === '编辑' && action.actionType === 'custom') {
              processedAction.onClick = (_: any, item: any) => {
                this.handleEditMenuItem(item);
              };
            } else if (action.label === '删除' && action.actionType === 'custom') {
              processedAction.onClick = (_: any, item: any) => {
                this.handleDeleteMenuItem(item);
              };
            }

            return processedAction;
          });
        } else {
          // 默认操作按钮配置
          menuItemActions = [
            {
              type: 'button',
              label: '编辑',
              level: 'link',
              size: 'sm',
              actionType: 'custom',
              onClick: (_: any, item: any) => {
                this.handleEditMenuItem(item);
              }
            },
            {
              type: 'button',
              label: '删除',
              level: 'danger',
              size: 'sm',
              actionType: 'custom',
              confirmText: '确定要删除这个菜单项吗？',
              onClick: (_: any, item: any) => {
                this.handleDeleteMenuItem(item);
              }
            }
          ];
        }
      }

      // 生成一个基于itemActions的key，确保操作栏变化时重新渲染
      const actionsHash = menuItemActions ? JSON.stringify(menuItemActions).length : 0;
      const navKey = `nav-${enableItemActions ? 'enabled' : 'disabled'}-${actionsHash}`;

      // 构建nav组件的props
      const navProps: any = {
        ...rest,
        key: navKey, // 添加key确保重新渲染
        draggable,
        saveOrderApi,
        dragOnSameLevel,
        searchable: finalSearchable,
        searchConfig: mergedSearchConfig,
        mini,
        enhance,
        accordion,
        stacked,
        mode,
        defaultOpenLevel,
        level,
        collapsed,
        expandIcon,
        expandPosition,
        themeColor,
        popupClassName,
        // 如果有finalLinksData就使用links，否则使用处理后的source
        ...(finalLinksData ? { links: finalLinks } : { source: processedSource }),
        deferApi,
        itemBadge,
        badge,
        overflow,
        showKey,
        themeCss,  // 直接传递themeCss给内部nav组件
        className: cx(
          setThemeClassName({
            name: 'navContentClassName',
            id,
            themeCss,
            ...this.props
          })
        ),
        // 导航项样式配置
        linkClassName: cx(
          setThemeClassName({
            name: 'navItemClassName',
            id,
            themeCss,
            ...this.props
          })
        ),
        type: 'nav'
      };

      // 只有在开启操作栏且有操作按钮时才添加itemActions属性
      if (enableItemActions === true && menuItemActions && menuItemActions.length > 0) {
        navProps.itemActions = menuItemActions;
      }


      const navComponent = render('my-nav', navProps);



      // 如果有导航头部配置，则包装导航组件
      if (header && header.enable) {
        return (
          <div
            className={cx(
              'my-nav-wrapper',
              className,
              setThemeClassName({
                name: 'baseControlClassName',
                id,
                themeCss,
                ...this.props
              })
            )}
            style={style}
          >
            {this.renderNavHeader()}
            <div
              className={cx(
                'my-nav-content',
                this.state.navCollapsed ? 'collapsed' : '',
                setThemeClassName({
                  name: 'navContentClassName',
                  id,
                  themeCss,
                  ...this.props
                })
              )}
            >
              {navComponent}
            </div>
            {this.renderAddDialog()}
            {this.renderEditDialog()}
            <CustomStyle
              {...this.props}
              config={{
                themeCss,
                classNames: [
                  {
                    key: 'baseControlClassName',
                    weights: {
                      default: {
                        important: true
                      },
                      hover: {
                        important: true
                      },
                      active: {
                        important: true
                      }
                    }
                  },
                  {
                    key: 'headerControlClassName',
                    weights: {
                      default: {
                        important: true
                      },
                      hover: {
                        important: true
                      },
                      active: {
                        important: true
                      }
                    }
                  },
                  {
                    key: 'headerTitleControlClassName',
                    weights: {
                      default: {
                        important: true
                      },
                      hover: {
                        important: true
                      },
                      active: {
                        important: true
                      }
                    }
                  },
                  {
                    key: 'navBtnControlClassName',
                    weights: {
                      default: {
                        important: true
                      },
                      hover: {
                        important: true
                      },
                      active: {
                        important: true
                      }
                    }
                  },
                  {
                    key: 'navContentClassName',
                    weights: {
                      default: {
                        important: true
                      },
                      hover: {
                        important: true
                      },
                      active: {
                        important: true
                      }
                    }
                  },
                  {
                    key: 'navItemClassName',
                    weights: {
                      default: {
                        important: true
                      },
                      hover: {
                        important: true
                      },
                      active: {
                        important: true
                      }
                    }
                  },
                  {
                    key: 'searchInputClassName',
                    weights: {
                      default: {
                        important: true
                      },
                      hover: {
                        important: true
                      },
                      active: {
                        important: true
                      }
                    }
                  }
                ],
                id
              }}
              env={env}
            />
          </div>
        );
      }

      return (
        <div
          className={cx(
            className,
            setThemeClassName({
              name: 'baseControlClassName',
              id,
              themeCss,
              ...this.props
            })
          )}
          style={style}
        >
          <div
            className={cx(
              setThemeClassName({
                name: 'navContentClassName',
                id,
                themeCss,
                ...this.props
              })
            )}
          >
            {navComponent}
          </div>
          {this.renderAddDialog()}
          {this.renderEditDialog()}
          <CustomStyle
            {...this.props}
            config={{
              themeCss,
              classNames: [
                {
                  key: 'baseControlClassName',
                  weights: {
                    default: {
                      important: true
                    },
                    hover: {
                      important: true
                    },
                    active: {
                      important: true
                    }
                  }
                },
                {
                  key: 'headerControlClassName',
                  weights: {
                    default: {
                      important: true
                    },
                    hover: {
                      important: true
                    },
                    active: {
                      important: true
                    }
                  }
                },
                {
                  key: 'headerTitleControlClassName',
                  weights: {
                    default: {
                      important: true
                    },
                    hover: {
                      important: true
                    },
                    active: {
                      important: true
                    }
                  }
                },
                {
                  key: 'navBtnControlClassName',
                  weights: {
                    default: {
                      important: true
                    },
                    hover: {
                      important: true
                    },
                    active: {
                      important: true
                    }
                  }
                },
                {
                  key: 'navContentClassName',
                  weights: {
                    default: {
                      important: true
                    },
                    hover: {
                      important: true
                    },
                    active: {
                      important: true
                    }
                  }
                },
                {
                  key: 'navItemClassName',
                  weights: {
                    default: {
                      important: true
                    },
                    hover: {
                      important: true
                    },
                    active: {
                      important: true
                    }
                  }
                },
                {
                  key: 'searchInputClassName',
                  weights: {
                    default: {
                      important: true
                    },
                    hover: {
                      important: true
                    },
                    active: {
                      important: true
                    }
                  }
                }
              ],
              id
            }}
            env={env}
          />
        </div>
      );
    }
    return <></>;
  }
}
