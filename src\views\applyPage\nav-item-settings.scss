/* 导航项设置按钮样式 */
.nav-item-settings-btn {
  opacity: 0;
  transition: opacity 0.2s ease;
  border-radius: 50% !important;
  width: 24px !important;
  height: 24px !important;
  min-width: 24px !important;
  min-height: 24px !important;
  padding: 0 !important;
  margin: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  background: rgba(0, 0, 0, 0.1) !important;
  border: none !important;
  position: relative !important;
  vertical-align: middle !important;
}

/* 确保设置按钮在导航项中垂直居中 */
.cxd-Nav-Menu-item .nav-item-settings-btn {
  position: absolute !important;
  right: 4px !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
}

/* 导航项悬浮时显示设置按钮 */
.cxd-Nav-Menu-item:hover .nav-item-settings-btn {
  opacity: 1;
}

/* 确保导航项有足够的右边距容纳设置按钮 */
.cxd-Nav-Menu-item {
  position: relative !important;
  padding-right: 10px !important;
}

/* 导航项链接样式调整 */
.cxd-Nav-Menu-item-link {
  padding-right: 4px !important;
}

/* 设置按钮悬浮效果 */
.nav-item-settings-btn:hover {
  background: rgba(0, 0, 0, 0.2) !important;
  transform: translateY(-50%) scale(1.1) !important;
}

/* 设置按钮图标样式 */
.nav-item-settings-btn .fa {
  font-size: 12px;
  color: #666;
}

/* 下拉菜单样式 */
.nav-item-settings-btn .cxd-DropDown-menu {
  min-width: 120px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid #e0e0e0;
  padding: 8px 0;
  background: white;
}

/* 下拉菜单项样式 */
.nav-item-settings-btn .cxd-DropDown-menu .nav-menu-item {
  justify-content: flex-start !important;
  padding: 12px 16px !important;
  border-radius: 0 !important;
  border: none !important;
  background: transparent !important;
  color: #333 !important;
  font-size: 14px !important;
  width: 100% !important;
  text-align: left !important;
  font-weight: normal !important;
  line-height: 1.4 !important;
}

.nav-item-settings-btn .cxd-DropDown-menu .nav-menu-item:hover {
  background: #f5f5f5 !important;
  color: #333 !important;
}

/* 删除按钮特殊样式 */
.nav-item-settings-btn .cxd-DropDown-menu .nav-menu-item-danger {
  background: #ff4d4f !important;
  color: white !important;
  margin: 4px 8px 0 8px !important;
  border-radius: 4px !important;
  font-weight: 500 !important;
}

.nav-item-settings-btn .cxd-DropDown-menu .nav-menu-item-danger:hover {
  background: #ff7875 !important;
  color: white !important;
}

/* 确保选中状态的样式优先级更高，不被悬浮效果覆盖 */
.cxd-Nav-Menu-item.cxd-Nav-Menu-item-active,
.cxd-Nav-Menu-item.cxd-Nav-Menu-item-selected {
  background-color: var(--colors-neutral-fill-8) !important;
  color: var(--colors-brand-6) !important;
}

/* 确保选中状态在悬浮时仍然保持 */
.cxd-Nav-Menu-item.cxd-Nav-Menu-item-active:hover,
.cxd-Nav-Menu-item.cxd-Nav-Menu-item-selected:hover {
  background-color: var(--colors-neutral-fill-8) !important;
  color: var(--colors-brand-6) !important;
}

/* 确保其他项目悬浮时不影响选中项 */
.cxd-Nav-Menu-item:not(.cxd-Nav-Menu-item-active):not(.cxd-Nav-Menu-item-selected):hover {
  background-color: var(--colors-neutral-fill-7) !important;
}

/* 强制选中状态的链接样式 */
.cxd-Nav-Menu-item.cxd-Nav-Menu-item-active .cxd-Nav-Menu-item-link,
.cxd-Nav-Menu-item.cxd-Nav-Menu-item-selected .cxd-Nav-Menu-item-link {
  color: var(--colors-brand-6) !important;
  background-color: transparent !important;
}

/* 强制选中状态的标签样式 */
.cxd-Nav-Menu-item.cxd-Nav-Menu-item-active .cxd-Nav-Menu-item-label,
.cxd-Nav-Menu-item.cxd-Nav-Menu-item-selected .cxd-Nav-Menu-item-label {
  color: var(--colors-brand-6) !important;
  font-weight: 500 !important;
}

/* 固定展开按钮样式 */
.fixed-expand-btn {
  position: absolute !important;
  top: 10px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  z-index: 1000 !important;
  width: 32px !important;
  height: 32px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  background-color: var(--body-bg) !important;
  border: 1px solid var(--borderColor) !important;
  border-radius: 4px !important;
  cursor: pointer !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

/* 固定展开按钮中的 SVG 样式 */
.fixed-expand-btn svg {
  width: 16px !important;
  height: 16px !important;
  stroke: currentColor !important;
  fill: none !important;
  transform: rotate(90deg) !important; /* 旋转箭头方向 */
}

/* 右键菜单样式增强 - 与 index.scss 中的样式配合使用 */
.customContextMenu {
  z-index: 999999 !important; /* 确保最高优先级，覆盖 index.scss 中的 99999 */
}
